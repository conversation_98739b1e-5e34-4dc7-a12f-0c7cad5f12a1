cmake_minimum_required(VERSION 3.16)

project(RoburGUI VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOMOC ON)
find_package(Qt6 6.4 REQUIRED COMPONENTS Quick)
find_package(Qt6 REQUIRED COMPONENTS Multimedia)
qt_standard_project_setup()

qt_add_executable(appRoburGUI
    resources/locale.qrc resources/icons.qrc resources/fonts.qrc
    qmldir
    main.cpp
)

qt_add_qml_module(appRoburGUI
    URI RoburGUI
    VERSION 1.0
    QML_FILES
    RESOURCES qml.qrc qmldir
    QML_FILES

)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
set_target_properties(appRoburGUI PROPERTIES
#    MACOSX_BUNDLE_GUI_IDENTIFIER com.example.appRoburGUI
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

target_link_libraries(appRoburGUI
    PRIVATE Qt6::Quick
)

include(GNUInstallDirs)
install(TARGETS appRoburGUI
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
