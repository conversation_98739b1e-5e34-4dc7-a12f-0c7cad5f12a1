<RCC>
    <qresource prefix="/">
        <file>Style.qml</file>
        <file>qmldir</file>
        <file>Components/RoburIconButton.qml</file>
        <file>Components/Numpad.qml</file>
        <file>Components/RoburSwitch.qml</file>
        <file>Testing/Test.qml</file>
        <file>Components/RoburCheckbox.qml</file>
        <file>main.qml</file>
        <file>Sections/MenuUser/MenuUser.qml</file>
        <file>Sections/Blocks/Block.qml</file>
        <file>Sections/GeneralLayout.qml</file>
        <file>CPPLauncher.qml</file>
        <file>Navigation/BarSideNavigation.qml</file>
        <file>Navigation/BarTopInfo.qml</file>
        <file>Navigation/SectionNavigator.qml</file>
        <file>Components/RoburText.qml</file>
        <file>Components/RoburDataViewer.qml</file>
        <file>CMakeLists.txt</file>
        <file>main.cpp</file>
        <file>qml.qrc</file>
        <file>Components/PasswordRobur.qml</file>
        <file>Components/PasswordNumber.qml</file>
        <file>Components/RoburCounterMini.qml</file>
        <file>Components/RoburCounterTime.qml</file>
        <file>Components/NameSectionBackground.qml</file>
        <file>Navigation/SavePage.qml</file>
        <file>Components/LanguageButton.qml</file>
        <file>Components/AnimatedContainer.qml</file>
        <file>Components/AnimatedConfirm.qml</file>
        <file>Components/RoburButtonDataviwer.qml</file>
        <file>Components/SwitcherType.qml</file>
        <file>Components/RoburRectangleBTN.qml</file>
        <file>Components/ConditionalLoader.qml</file>
        <file>Testing/LoadPage.qml</file>
        <file>Components/RoburConfirm.qml</file>
        <file>Testing/LoaderPage.qml</file>
        <file>Components/RoburMessage.qml</file>
        <file>Sections/Home/ModelHomePageRow.qml</file>
        <file>Sections/Home/PageHome.qml</file>
        <file>Sections/Information/PageCoolMachineStatus.qml</file>
        <file>Sections/Information/PageHeatMachineStatus.qml</file>
        <file>Sections/Information/InformationComponents/ModelCoolMachineStatus.qml</file>
        <file>Sections/Information/InformationComponents/ModelHeatMachineStatus.qml</file>
        <file>Sections/Information/InformationComponents/ModelHistoryEvent.qml</file>
        <file>Sections/Information/InformationComponents/ModelDataImplant.qml</file>
        <file>Sections/Information/InformationComponents/ModelInformationMachine.qml</file>
        <file>Sections/Information/PageDataMachine.qml</file>
        <file>Sections/Information/PageHistoryEvent.qml</file>
        <file>Sections/Information/PageInformationMachine.qml</file>
        <file>Sections/Information/MenuInformation.qml</file>
        <file>Sections/Alarms/ModelAlarmConfiguration.qml</file>
        <file>Sections/Alarms/PageAlarm.qml</file>
        <file>Sections/Information/PageInformationDDCImplant.qml</file>
        <file>Sections/MenuUser/MenuCoolService.qml</file>
        <file>Sections/MenuUser/MenuHeatService.qml</file>
        <file>Sections/MenuUser/PagePreferences.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/ModelLanguage.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/ModelWaterBands.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/PickerDate.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/PickerTime.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/AmbientSettingsSetPoints.qml</file>
        <file>Sections/MenuUser/ComponentsUserSection/AmbientSettingsTimeSlots.qml</file>
        <file>Sections/MenuSettings/PageDDCImplantControl.qml</file>
        <file>Sections/MenuSettings/MenuDDC.qml</file>
        <file>Sections/MenuSettings/PageAgree.qml</file>
        <file>Sections/MenuSettings/PageBlockInstallConfiguration.qml</file>
        <file>Sections/MenuSettings/MenuImplant.qml</file>
        <file>Sections/MenuSettings/MenuSettings.qml</file>
        <file>Sections/MenuSettings/PageMachineManagement.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ConfigurationSection/ConfigurationIdSetting.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ConfigurationSection/ConfigurationMachine.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ConfigurationSection/ConfigurationMachineACSBase.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ConfigurationSection/ConfigurationMachineACSSeparabile.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ConfigurationSection/DiscoveryModel.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ModelCheckConfiguration.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ModelElementComunicationParameter.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/ModelThirdPartCategory.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/PageCheckConfiguration.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/PageComunicationParameters.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/PageFactoryResetSoftwareUpdate.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/PageInitMachineConfiguration.qml</file>
        <file>Sections/MenuSettings/SubSectionDDC/PageThirdPartCategory.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/ConfigurationService/ServiceAgreeDCCRbox.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/ConfigurationService/ServiceCurva.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/ConfigurationService/ServiceLimitPower.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/ConfigurationService/ServiceTemoSetPoint.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/ConfigurationACSBase.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageDifferentialWaterInitialPowerBolierPowerImplant.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageErrorConfigurationTempetarure.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/MenuConfigurationService.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageConfirmRegolationParemeterBase.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageParameterRegolationBase.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PagePriorityUseMachine.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageRBOXCircularDelay.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase/PageInversionParametersValve.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageRBOXCircularDelaySwitchingPhaseValveParameters.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageGAHRegulationMode.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageInitialPowerNominalPowerImplant.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageConfirmParameterRegolationSeparable.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageACSWaterSetpointWaterDifferential.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageDDCSetParameter.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable/PageThirdCategoriesSetParameter.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuBase.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/MenuSeparable.qml</file>
        <file>Sections/MenuSettings/SubSectionImplant/PageCircolarExitAlarms.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/AgreeComponent.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/AgreeComponentPartial.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/AgreePieceHorizontal.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/AgreePieceHorizontalAnimated.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/AgreePieceVertical.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/ModelAgree.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/ModelMachineManagament.qml</file>
        <file>Sections/MenuSettings/SettingsComponents/ModelParRegBase.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageACSBase.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageACSSeparable.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageAmbientSettings.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageExternAmbientSettings.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageTODOSilenceMod.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageWaterBandsSettings.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/PageWaterSetpoint.qml</file>
        <file>Sections/Root/RootAlarm.qml</file>
        <file>Sections/Root/RootHome.qml</file>
        <file>Sections/Root/RootInformation.qml</file>
        <file>Sections/Root/RootSettings.qml</file>
        <file>Sections/Root/RootUser.qml</file>
        <file>Sections/MenuUser/MenuUserSubSection/ModelWaterSetpoint.qml</file>
    </qresource>
</RCC>
