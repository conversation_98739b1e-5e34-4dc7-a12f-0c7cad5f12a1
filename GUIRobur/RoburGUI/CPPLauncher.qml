import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "Testing"
import "Components"
import "Sections/Blocks"
import "Sections/Alarms"
import "Sections/MenuSettings"
import "Sections/MenuUser"
import "Sections/MenuUser/MenuUserSubSection"
import "Sections/Home"
import "Sections/Information"
import "Navigation"
import "Sections"
ApplicationWindow {
    id : mainArea
    visible: true
    color: Style.colors.lightGray
    width: Style.dimensions.windowWidth
    height: Style.dimensions.windowHeight
    minimumWidth: 720
    minimumHeight: 490
    RowLayout {
        anchors.fill: parent
        //=============================================================
        // SIDE BAR NAVIGATION
        //=============================================================
        //BarSideNavigation{z : 1}
        Item{
            Layout.fillWidth: true
            Layout.fillHeight:  true
            ColumnLayout {
                anchors.fill: parent
                //=============================================================
                // TOP BAR INFORMATION FOR ALL THE TIME
                //=============================================================
                // BarTopInfo{
                    //     Layout.topMargin: 4
                    //     Layout.alignment : Qt.AlignTop
                    // }
                    //TimePicker{}
                    Item {
                        Layout.fillWidth: true
                        Layout.fillHeight:  true
                        RoburCheckbox
                        {
                            controlCheck: true
                            anchors.centerIn: parent
                        }
                    }
                }
            }
        }
    }
