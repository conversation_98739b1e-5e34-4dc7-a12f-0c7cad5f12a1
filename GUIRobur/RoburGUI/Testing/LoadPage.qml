import QtQuick 2.15
import QtQuick.Layouts 2.15
import "../"
Rectangle {
    id: loader
    property int heightToFill
    property bool when : false
    state: "CloseLoad"
    height: 0
    color:"pink"
    radius: 4
    z: 1
    states: [
    State {
        name: "Load"
        when: loader.when
        PropertyChanges {
            target: loader
            color: Style.colors.darkerOrange
            height:heightToFill
        }
    },
    State {
        name: "CloseLoad"
        when:  !loader.when
        PropertyChanges {
            target: loader
            color: Style.colors.orange
            height: 0
        }
    }
    ]
    transitions: [
    Transition {
        from: "CloseLoad"
        to: "Load"
        SequentialAnimation {
            ParallelAnimation {
                NumberAnimation {
                    target: loader
                    property: "height"
                }
                ColorAnimation {
                    target: loader
                    property: "color"
                }
            }
        }
    },
    Transition {
        from: "Load"
        to: "CloseLoad"
        ParallelAnimation {
            NumberAnimation {
                target: loader
                property: "height"
            }
            ColorAnimation {
                target: loader
                property: "color"
            }
        }
    }
    ]
}
