import QtQuick 2.15
Item {
    id: root
    property int heightToFill
    property bool whenLoad: false
    property bool whenDisappier: false
    state: "Hidden"
    width: parent ? parent.width : 0
    height: heightToFill
    y: -heightToFill
    z: 1
    states: [
    State {
        name: "Visible"
        when: root.whenLoad
        PropertyChanges {
            target: root
            y: 0
        }
    },
    State {
        name: "Hidden"
        when: root.whenDisappier
        PropertyChanges {
            target: root
            y: -heightToFill
        }
    }
    ]
    transitions: [
    Transition {
        from: "Hidden"
        to: "Visible"
        NumberAnimation {
            target: root
            property: "y"
            duration: 300
            easing.type: Easing.InOutQuad
        }
    },
    Transition {
        from: "Visible"
        to: "Hidden"
        NumberAnimation {
            target: root
            property: "y"
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }
    ]
}
