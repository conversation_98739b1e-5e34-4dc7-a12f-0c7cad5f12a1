import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    id : root
    /*
    function getColorByValue(val){
        if(val==JsonConf.Timeslot_off)
        return "transparent";
        if(val==JsonConf.Timeslot_antifreeze)
        return Style.colors.blue;
        if(val==JsonConf.Timeslot_reduced)
        return Style.colors.green;
        if(val==JsonConf.Timeslot_comfort)
        return Style.colors.red;
        if(val==JsonConf.Timeslot_speed_1)
        return Style.colors.red;
        if(val==JsonConf.Timeslot_speed_2)
        return Style.colors.orange;
        if(val==JsonConf.Timeslot_speed_3)
        return Style.colors.green;
        if(val==JsonConf.Timeslot_speed_4)
        return Style.colors.blue;
        return Style.colors.t
    }
    */
    function getColorContrast(val)
    {
        if(val == 0)
        {
            return Style.colors.white;
        }
        if(val == 1)
        {
            return Style.colors.orangeContrast;
        }
        if(val == 2)
        {
            return Style.colors.deepBlue;
        }
        if(val == 3)
        {
            return Style.colors.cyan;
        }
    }
    function getColorByValue(val)
    {
        if(val == 0)
        {
            return Style.colors.darkerGray;
        }
        if(val == 1)
        {
            return Style.colors.blue;
        }
        if(val == 2)
        {
            return Style.colors.green;
        }
        if(val == 3)
        {
            return Style.colors.red;
        }
    }
    Rectangle{
        width: 52 * 13
        height: 52 * 5
        radius: 4
        anchors.centerIn: parent
        GridView{
            id:timeSlotList
            anchors.centerIn: parent
            width: parent.width-8
            height: parent.height-8
            interactive: false
            cellWidth: width / Math.floor(width / 52);
            cellHeight: height / Math.floor(height / 52);
            clip : true
            model: ambient.model
            delegate: Item{
                required property var modelData
                required property var index
                width: GridView.view.cellWidth
                height: GridView.view.cellHeight
                Rectangle{
                    anchors.verticalCenter: parent.verticalCenter
                    anchors.left: parent.left
                    anchors.right: parent.right
                    height: 52
                    anchors.leftMargin:  modelData.left.value ? -12 : Math.floor((parent.width-52)/2)
                    anchors.rightMargin:  modelData.right.value? -12 : Math.floor((parent.width-52)/2)
                    color: getColorByValue(modelData.value.value)
                    radius:4
                }
                RoburText {
                    anchors.centerIn: parent
                    text: modelData.time.value
                    color:  modelData.sequence.value ?Style.colors.white: getColorContrast(modelData.value.value)
                    //size: Style.dimensions.fontTinySize
                    width: (modelData.left.value ||   modelData.right.value) ?  parent.width - 25 : parent.width - 20
                    height: parent.height/2
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    wrapMode: Text.NoWrap
                }
            }
            MouseArea{
                anchors.fill: parent
                id:locator
                property int currentId: -1
                propagateComposedEvents: true
                property int index: timeSlotList.indexAt(mouseX, mouseY)
                hoverEnabled: true
                onPressed: {
                    ambient.startSwipe(timeSlotList.indexAt(mouseX, mouseY));
                }
                onReleased: {
                    if(ambient.startPosition >=0 && ambient.imIn.value)
                    ambient.endSwipe( timeSlotList.indexAt(mouseX, mouseY));
                }
                onIndexChanged:   {
                    if(ambient.startPosition >=0){
                        ambient.updateTimeslotOnSwipe(index);
                    }
                }
                onMouseXChanged: {
                    if(mouseX >= locator.x && mouseX <=locator.x + locator.width)
                    ambient.imIn.value = true
                    else{
                        ambient.imIn.value = false
                        ambient.startPosition = -1;
                    }
                }
                onMouseYChanged: {
                    if(mouseY >= locator.y && mouseY <=locator.y + locator.height)
                    {
                        ambient.imIn.value = true
                    }
                    else{
                        ambient.imIn.value = false
                        ambient.startPosition = -1;
                    }
                }
            }
        }
    }
}
