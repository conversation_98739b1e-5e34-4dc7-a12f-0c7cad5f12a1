import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../"
import "../../../Components"
import "../../../Testing"
ListModel
{
    ListElement
    {
        nationIcon: "qrc:/icons/italy.png"
        nationLanguage : qsTr("italiano")
        language :"it"
    }
    ListElement{
        nationIcon: "qrc:/icons/spain.png"
        nationLanguage : qsTr("espanol")
        language :"es"
    }
    ListElement{
        nationIcon: "qrc:/icons/france.png"
        nationLanguage : qsTr("francais")
        language :"fr"
    }
    ListElement{
        nationIcon: "qrc:/icons/poland.png"
        nationLanguage : qsTr("polsky")
        language :"pl"
    }
    ListElement{
        nationIcon: "qrc:/icons/czech.png"
        nationLanguage : qsTr("ceský")
        language :"cs"
    }
    ListElement{
        nationIcon: "qrc:/icons/united-kingdom.png"
        nationLanguage : qsTr("english")
        language :"en"
    }
    ListElement{
        nationIcon: "qrc:/icons/germany.png"
        nationLanguage : qsTr("deutsch")
        language :"de"
    }
    ListElement{
        nationIcon: "qrc:/icons/turkey.png"
        nationLanguage : qsTr("turkey")
        language :"tr"
    }
    ListElement{
        nationIcon: "qrc:/icons/russia.png"
        nationLanguage : qsTr("russia")
        language :"ru"
    }
    ListElement{
        nationIcon: "qrc:/icons/romania.png"
        nationLanguage : qsTr("romania")
        language :"ro"
    }
    ListElement{
        nationIcon: "qrc:/icons/latvian.png"
        nationLanguage : qsTr("latvian")
        language :"hr"
    }
    ListElement{
        nationIcon: "qrc:/icons/lithuania.png"
        nationLanguage : qsTr("lithuania")
        language :"lt"
    }
    ListElement{
        nationIcon: "qrc:/icons/ungheria.png"
        nationLanguage : qsTr("ungheria")
        language :"hu"
    }
    ListElement{
        nationIcon: "qrc:/icons/croatia.png"
        nationLanguage : qsTr("croatia")
        language :"hr"
    }
    ListElement{
        nationIcon: "qrc:/icons/norway.png"
        nationLanguage : qsTr("norway")
        language :"no"
    }
}
