import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    id: root
    property int selected: -1
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    RowLayout{
        id : containerSection
        anchors.top: parent .top
        anchors.topMargin: 8
        anchors.left: parent.left
        anchors.right: parent.right
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("setpoint") +  " " + qsTr("chrono_thermostat")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("setpoint temperatura esterna") + " " + qsTr("temperatura_esterna_filtrata")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                }
            }
        }
    }
    RowLayout {
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        anchors.topMargin: 16
        spacing: 8
        RoburCounterMini{
            id : t1
            label: qsTr("chrono_thermostat_t1")
            activeColor: Style.colors.blue
            candidateValue:  preferencePage.temperatureBox.value ?ambient.t1Value.value   : (ambient.t1Value.value * 9/5) + 32
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            enableTick: true
            btnSize :  Style.dimensions.cnButtonRoburCounter
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: firstBox.width /2
            onDownClicked: {
                ambient.t1Value.value -=0.1;
            }
            onUpClicked: {
                ambient.t1Value.value+= 0.1;
            }
            onDisableAll: {
                t2.isActive = false
                t3.isActive = false
            }
        }
        RoburCounterMini{
            id : t2
            label: qsTr("chrono_thermostat_t2")
            activeColor: Style.colors.green
            candidateValue:  preferencePage.temperatureBox.value ?ambient.t2Value.value   : (ambient.t2Value.value * 9/5) + 32
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            enableTick: true
            btnSize :  Style.dimensions.cnButtonRoburCounter
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: firstBox.width /2
            onDownClicked: {
                ambient.t2Value.value -=0.1;
            }
            onUpClicked: {
                ambient.t2Value.value+= 0.1;
            }
            onDisableAll: {
                t1.isActive = false
                t3.isActive = false
            }
        }
        RoburCounterMini{
            id : t3
            label: qsTr("chrono_thermostat_t3")
            activeColor: Style.colors.red
            candidateValue:  preferencePage.temperatureBox.value ?ambient.t3Value.value   : (ambient.t3Value.value * 9/5) + 32
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            Layout.fillWidth: true
            Layout.fillHeight: true
            btnSize :  Style.dimensions.cnButtonRoburCounter
            isAnimated: true
            widthToOccupe: firstBox.width /2
            enableTick: true
            onDownClicked: {
                ambient.t3Value.value -= 0.1;
            }
            onUpClicked: {
                ambient.t3Value.value+= 0.1;
            }
            onDisableAll: {
                t1.isActive = false
                t2.isActive = false
            }
        }
    }
    RowLayout {
        id : secondBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 200
        anchors.topMargin: 16
        spacing: 8
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight: true}
            RoburCounterMini{
                label:   qsTr("setpoint") +  " " + qsTr("differential")
                activeColor: Style.colors.blue
                candidateValue:  preferencePage.temperatureBox.value ?ambient.t1Value.value   : (ambient.t1Value.value * 9/5) + 32
                unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
                enableTick: true
                btnSize :  Style.dimensions.cnButtonRoburCounter
                Layout.fillWidth: true
                Layout.fillHeight: true
                isAnimated: true
                widthToOccupe: secondBox.width
                heightToOccupe: secondBox.height
                onDownClicked: {
                    ambient.t1Value.value -=0.1;
                }
                onUpClicked: {
                    ambient.t1Value.value+= 0.1;
                }
            }
            AnimatedContainer{
                Layout.fillWidth: true
                Layout.fillHeight: true}
            }
        }
