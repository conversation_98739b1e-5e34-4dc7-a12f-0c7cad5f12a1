﻿import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../"
import "../../../Components"
import "../../../Testing"
Item{
    id : root
    anchors.fill: parent
    property var onHour
    property var offHour
    property var onMinute
    property var offMinute
    property var temperature
    property var timer
    property var onOff
    property real valTemperature : root.temperature.value
    property alias onTextBand : onBand.label
    property alias offTextBand : offBand.label
    property alias temperatureText : temperature.label
    signal incrementTime
    signal decrementTime
    RowLayout {
        id: row
        spacing: 16
        anchors.fill: parent
        RoburCounterTime{
            id : onBand
            candidateHour: root.onHour.value
            candidateMinute: root.onMinute.value
            label: ambient.t1Setter.value
            activeColor: Style.colors.blue
            widthToOccupe: row.width
            heightToOccupe: row.height
            enableTick: true
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            onUpClicked: {
                var temp = parseInt(onMinute.value) + 10;
                onMinute.value = temp
                waterBand.normalizeTime(onHour,onMinute)
            }
            onDownClicked: {
                onMinute.value -= 10
                waterBand.normalizeTime(onHour,onMinute)
            }
            onDisableAll: {
                offBand.isActive = false
                temperature.isActive = false
            }
        }
        RoburCounterTime{
            id : offBand
            candidateHour: root.offHour.value
            candidateMinute: root.offMinute.value
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: row.width
            heightToOccupe: row.height
            label: ambient.t2Setter.value
            activeColor: Style.colors.green
            enableTick: true
            onUpClicked: {
                var temp = parseInt(offMinute.value) + 10;
                offMinute.value = temp
                waterBand.normalizeTime(offHour,offMinute)
            }
            onDownClicked: {
                offMinute.value -= 10
                waterBand.normalizeTime(offHour,offMinute)
            }
            onDisableAll: {
                onBand.isActive = false
                temperature.isActive = false
            }
        }
        RoburCounterMini{
            id : temperature
            candidateValue:  preferencePage.temperatureBox.value ? root.temperature.value   : (root.temperature.value * 9/5) + 32
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            activeColor: Style.colors.red
            enableTick: true
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: row.width
            heightToOccupe: row.height
            onUpClicked: {
                var temp = parseFloat(root.temperature.value) + 0.1
                root.temperature.value = temp
            }
            onDownClicked:
            {
                var temp = parseFloat(root.temperature.value) - 0.1
                root.temperature.value = temp
            }
            onDisableAll: {
                onBand.isActive = false
                offBand.isActive = false
            }
        }
        AnimatedContainer{
            id : checkboxArea
            Layout.fillWidth:  true
            Layout.fillHeight: true
            widthToOccupe: row.width /2
            heightToOccupe: row.height /2
            isAnimated: true
            isActive : root.onOff.value
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            Layout.rightMargin: 16
            RoburCheckbox
            {
                controlCheck :true
                width: parent.width
                height: parent.height
                isTexted: false
                firstColor:parent.isActive ? Style.colors.white :  Style.colors.darkerGray
                secondColor:parent.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                onClicked:
                {
                    root.onOff.value  = !root.onOff.value
                }
            }
            onClicked :
            {
                root.onOff.value  = !root.onOff.value
            }
        }
    }
}
