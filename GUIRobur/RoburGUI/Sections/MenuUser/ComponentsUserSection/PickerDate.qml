import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../"
import "../../../Components"
import "../../../Testing"
Item{
    id : rootData
    Layout.fillHeight: true
    Layout.fillWidth: true
    RowLayout
    {
        anchors.fill: parent
        RoburCounterMini{
            id : day
            candidateValue: dataTime.day.value == 0 ? 1234566 : dataTime.day.value
            isAnimated: true
            needToFix: false
            inactiveColor: Style.colors.white
            z : {
                if(month.isZActive || year.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            label: qsTr("days")
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootData.width
            heightToOccupe: rootData.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onClicked: {
            }
            onDisableAll: {
                year.isActive = false
                month.isActive = false
            }
        }
        RoburCounterMini{
            id : month
            label: qsTr("months")
            isAnimated: true
            needToFix: false
            candidateValue: dataTime.month.value
            inactiveColor: Style.colors.white
            z : {
                if(day.isZActive || year.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootData.width
            heightToOccupe: rootData.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onDisableAll: {
                day.isActive = false
                year.isActive = false
            }
        }
        RoburCounterMini{
            id : year
            label: qsTr("years")
            isAnimated: true
            needToFix: false
            candidateValue: dataTime.year.value
            inactiveColor: Style.colors.white
            z : {
                if(day.isZActive || month.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootData.width
            heightToOccupe: rootData.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onDisableAll: {
                day.isActive = false
                month.isActive = false
            }
        }
    }
}
