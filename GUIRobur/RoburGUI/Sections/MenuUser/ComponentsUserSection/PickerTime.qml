import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../"
import "../../../Components"
import "../../../Testing"
Item{
    id : rootTime
    Layout.fillHeight: true
    Layout.fillWidth: true
    RowLayout
    {
        anchors.fill: parent
        RoburCounterMini{
            id : hour
            isAnimated: true
            needToFix: false
            candidateValue: dataTime.hour.value
            inactiveColor: Style.colors.white
            z : {
                if(minute.isZActive || second.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            label: qsTr("hours")
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootTime.width
            heightToOccupe: rootTime.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onClicked: {
            }
            onDisableAll: {
                minute.isActive = false
                second.isActive = false
            }
        }
        RoburCounterMini{
            id : minute
            isAnimated: true
            label: qsTr("minutes")
            candidateValue: dataTime.minute.value
            needToFix: false
            inactiveColor: Style.colors.white
            z : {
                if(hour.isZActive || second.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootTime.width
            heightToOccupe: rootTime.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onDisableAll: {
                hour.isActive = false
                second.isActive = false
            }
        }
        RoburCounterMini{
            id : second
            isAnimated: true
            label: qsTr("seconds")
            needToFix: false
            candidateValue: dataTime.second.value
            inactiveColor: Style.colors.white
            z : {
                if(hour.isZActive || minute.isZActive){
                    return -1;
                }
                else
                return 0;
            }
            textSize: 40
            btnSize: 45
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: rootTime.width
            heightToOccupe: rootTime.height
            unit: ""
            enableTick: true
            onDownClicked: {
            }
            onUpClicked: {
            }
            onDisableAll: {
                hour.isActive = false
                minute.isActive = false
            }
        }
    }
}
