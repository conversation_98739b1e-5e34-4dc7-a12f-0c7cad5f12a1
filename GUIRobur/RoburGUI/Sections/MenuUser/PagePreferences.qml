import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../"
import "../../Components"
import "../../Testing"
import "ComponentsUserSection"
Item{
    id: frame
    //anchors.centerIn: parent
    Layout.fillWidth: true
    Layout.fillHeight: true
    Flickable {
        id : flickable
        anchors.fill: parent
        contentWidth: frame.width
        contentHeight:  preferencePage.blockKeyBoard.value ?  frame.height * 3 + 16 : frame.height * 2 + 8  // Ensure content height exceeds the viewport height for scrolling
        clip: true
        boundsBehavior: Flickable.StopAtBounds
        ScrollIndicator.vertical: ScrollIndicator {
            parent: flickable.parent
            anchors.top: flickable.top
            anchors.bottom: flickable.bottom
            anchors.left: flickable.right
            width: 11
            active: true
            visible: true  // Show it always
            opacity: 1.0   // Fully visible
            // Prevent Flickable from deactivating it
            onActiveChanged: {
                if (!active) active = true;
            }
        }
        //boundsBehavior: Flickable.StopAtBounds
        Item{
            id : firstContainer
            width: frame.width
            height: frame.height
            ColumnLayout{
                anchors.fill: parent
                spacing: 8
                Rectangle{
                    radius : Style.dimensions.radiusCorner
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    GridView {
                        id : grid
                        anchors.fill: parent
                        boundsBehavior: Flickable.StopAtBounds
                        clip: true
                        cellWidth : grid.width/ 3
                        cellHeight :grid.height /3
                        //TODO FIX
                        // ScrollIndicator.vertical: ScrollIndicator {
                            //     parent: flickable.parent
                            //     anchors.top: flickable.top
                            //     anchors.left: flickable.right
                            //     anchors.bottom: flickable.bottom
                            // }
                            model: preferencePage.model
                            focus: true
                            delegate:
                            Item {
                                id : wrapper
                                required property var modelData
                                required property int index
                                width: grid.cellWidth; height: grid.cellHeight
                                LanguageButton{
                                    id : languageBTN
                                    icon: modelData.languageIconPath.value
                                    anchors.verticalCenter: parent.verticalCenter
                                    anchors.left: parent.left
                                    anchors.leftMargin: 16
                                    // isSelected: wrapper.GridView.isCurrentItem
                                    isSelected : modelData.languageValue.value === preferencePage.currentLanguage.value
                                    width: 60
                                    height: 60
                                    onClicked: {
                                        grid.currentIndex = index
                                        preferencePage.currentLanguage.value = modelData.languageValue.value
                                        translator.currentLocale = modelData.languageValue.value
                                    }
                                }
                                RoburText
                                {
                                    anchors.verticalCenter: languageBTN.verticalCenter
                                    anchors.left: languageBTN.right
                                    text: modelData.languageText.value
                                    anchors.leftMargin: 16
                                    color:   modelData.languageValue.value === preferencePage.currentLanguage.value ? Style.colors.orange : Style.colors.darkGray
                                }
                            }
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RowLayout{
                            anchors.fill: parent
                            spacing: 8
                            PickerDate{}
                            PickerTime{}
                        }
                    }
                }
            }
            Item{
                id : secondoContainer
                width: frame.width
                height: frame.height
                anchors.top: firstContainer.bottom
                anchors.topMargin: 8
                GridLayout{
                    anchors.fill: parent
                    columns:2
                    columnSpacing: 8
                    rowSpacing: 8
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        NameSectionBackground{
                            id : idUnitaDiMisura
                            iconPath : "qrc:/icons/ruler.png"
                            label:  qsTr("unita_misura_temperatura")
                        }
                        ColumnLayout
                        {
                            anchors.fill: parent
                            anchors.leftMargin: 16
                            anchors.topMargin: 64
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    id : checkCelcius
                                    labelName : qsTr("gradi_centigradi")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck:  preferencePage.temperatureBox.value
                                }
                                MouseArea{
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.temperatureBox.value = true
                                    }
                                }
                            }
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    id : checkFahrenheit
                                    labelName : qsTr("gradi_fahrenheit")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck:  !preferencePage.temperatureBox.value
                                }
                                MouseArea{
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.temperatureBox.value = false
                                    }
                                }
                            }
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        NameSectionBackground{
                            iconPath : "qrc:/icons/lightbulb.png"
                            label:  qsTr("title_energy_saver")
                        }
                        GridLayout{
                            id : layout
                            anchors.fill: parent
                            anchors.leftMargin: 16
                            anchors.topMargin: 64
                            rows: 2
                            columns: 2
                            property var options: ["15", "30", "1", "2"]
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    labelName : qsTr("minuti_1")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck: preferencePage.timeCheckbox1Min.value
                                }
                                MouseArea
                                {
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.timeCheckbox15sec.value = false
                                        preferencePage.timeCheckbox30sec.value = false
                                        preferencePage.timeCheckbox1Min.value = true
                                        preferencePage.timeCheckbox2Min.value = false
                                    }
                                }
                            }
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    labelName :qsTr("secondi_15")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck: preferencePage.timeCheckbox15sec.value
                                }
                                MouseArea
                                {
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.timeCheckbox15sec.value = true
                                        preferencePage.timeCheckbox30sec.value = false
                                        preferencePage.timeCheckbox1Min.value = false
                                        preferencePage.timeCheckbox2Min.value = false
                                    }
                                }
                            }
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    labelName :qsTr("minuti_2")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck: preferencePage.timeCheckbox2Min.value
                                }
                                MouseArea{
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.timeCheckbox15sec.value = false
                                        preferencePage.timeCheckbox30sec.value = false
                                        preferencePage.timeCheckbox1Min.value = false
                                        preferencePage.timeCheckbox2Min.value = true
                                    }
                                }
                            }
                            Item{
                                Layout.fillHeight: true
                                Layout.fillWidth: true
                                RoburCheckbox{
                                    labelName :qsTr("secondi_30")
                                    Layout.alignment: Qt.AlignHCenter
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    controlCheck: preferencePage.timeCheckbox30sec.value
                                }
                                MouseArea
                                {
                                    anchors.fill: parent
                                    onClicked: {
                                        preferencePage.timeCheckbox15sec.value = false
                                        preferencePage.timeCheckbox30sec.value = true
                                        preferencePage.timeCheckbox1Min.value = false
                                        preferencePage.timeCheckbox2Min.value = false
                                    }
                                }
                            }
                        }
                    }
                    Item{
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        NameSectionBackground{
                            id : idUnlock
                            iconPath :  preferencePage.blockKeyBoard.value? "qrc:/icons/lock.png" : "qrc:/icons/unlock.png"
                            label:  "blocco tastiera"
                            isSelected:  preferencePage.blockKeyBoard.value
                        }
                        RoburSwitch {
                            id : idSwitchBlockKeyboard
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.margins: 16
                            width: 60
                            height: 30
                            secondOptionText: qsTr("no")
                            firstOptionText: qsTr("yes")
                            isChecked: preferencePage.blockKeyBoard.value
                            activeBaseColor: Style.colors.white
                            inactiveBaseColor: Style.colors.darkGray
                            pivotDisabledBorderColor: Style.colors.darkGray
                            pivotUpBorderColor: Style.colors.white
                            pivotUpColor: Style.colors.darkOrange
                        }
                        MouseArea{
                            anchors.fill: parent
                            onClicked: {
                                preferencePage.blockKeyBoard.value = !preferencePage.blockKeyBoard.value
                            }
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        NameSectionBackground{
                            id : idWifi
                            iconPath : preferencePage.wifiConnection.value ? "qrc:/icons/wifi.png" : "qrc:/icons/no-wifi.png"
                            label:  qsTr("internet_connection")
                            isSelected: preferencePage.wifiConnection.value
                        }
                        RoburSwitch {
                            id : idSwitchInternet
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.margins: 16
                            width: 60
                            height: 30
                            isChecked: preferencePage.wifiConnection.value
                            firstOptionText : qsTr("net_type_eth")
                            secondOptionText: qsTr("net_type_none")
                            activeBaseColor: Style.colors.white
                            inactiveBaseColor: Style.colors.darkGray
                            pivotDisabledBorderColor: Style.colors.darkGray
                            pivotUpBorderColor: Style.colors.white
                            pivotUpColor: Style.colors.darkOrange
                        }
                        MouseArea{
                            anchors.fill: parent
                            onClicked: {
                                preferencePage.wifiConnection.value = !preferencePage.wifiConnection.value
                            }
                        }
                    }
                }
            }
            Item{
                id : thirdContainer
                property bool passwordChange: false
                width: frame.width
                height: frame.height
                anchors.top: secondoContainer.bottom
                anchors.topMargin: 8
                visible: preferencePage.blockKeyBoard.value
                RowLayout{
                    anchors.fill: parent
                    Item {
                        Layout.preferredWidth: 450
                        Layout.fillHeight: true
                        Timer {
                            id : timer
                            interval: 250;
                            repeat: false;
                            running: false;
                            onTriggered: {thirdContainer.passwordChange = true;restartTimer.start() }
                        }
                        Timer {
                            id : restartTimer
                            interval: 2000;
                            repeat: false;
                            running: false;
                            onTriggered: {thirdContainer.passwordChange = false }
                        }
                        NameSectionBackground{
                            iconPath :thirdContainer.passwordChange  ? (preferencePage.password.passwordChageSetter.value ? "qrc:/icons/key.png" : "qrc:/icons/lock.png") : "qrc:/icons/unlock.png"
                            label: thirdContainer.passwordChange  ? (preferencePage.password.passwordChageSetter.value ? qsTr("changed") : qsTr("unchanged")) : qsTr("password")
                            color :  thirdContainer.passwordChange  ? (preferencePage.password.passwordChageSetter.value ? Style.colors.green : Style.colors.red) : Style.colors.darkGray
                            colorImage: thirdContainer.passwordChange  ? (preferencePage.password.passwordChageSetter.value ? Style.colors.green : Style.colors.red) : Style.colors.darkGray
                        }
                        RoburIconButton{
                            icon : "qrc:/icons/arrow.png"
                            isSelected: preferencePage.password.turnSetter.value
                            anchors.right: pass.left
                            anchors.rightMargin: 16
                            anchors.verticalCenter: pass.verticalCenter
                            rotation: 180
                            onClicked: {
                                preferencePage.password.turnSetter.value = !preferencePage.password.turnSetter.value
                                pass.index = 0;
                            }
                        }
                        RoburIconButton{
                            icon : "qrc:/icons/arrow.png"
                            isSelected: !preferencePage.password.turnSetter.value
                            anchors.right: ripass.left
                            anchors.rightMargin: 16
                            anchors.verticalCenter: ripass.verticalCenter
                            rotation: 180
                            onClicked: {
                                preferencePage.password.turnSetter.value = !preferencePage.password.turnSetter.value
                                ripass.index = 0;
                            }
                        }
                        RoburText{
                            id : text1
                            text: qsTr("button_enter_password") + " : "
                            anchors.top: parent.top
                            anchors.topMargin: 80
                            anchors.left: parent.left
                            anchors.leftMargin:80
                        }
                        PasswordRobur{
                            id : pass
                            anchors.top: text1.top
                            anchors.topMargin: 32
                            anchors.left: text1.left
                            model: preferencePage.password.insertPasswordModel
                        }
                        RoburText{
                            id : text2
                            text: qsTr("ripeti_password_corrente")
                            anchors.top: pass.bottom
                            anchors.topMargin: 48
                            anchors.left: text1.left
                        }
                        PasswordRobur{
                            id : ripass
                            anchors.top: text2.top
                            anchors.topMargin: 32
                            anchors.left: text2.left
                            model:preferencePage.password.reinsertPasswordModel
                        }
                    }
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        radius : Style.dimensions.radiusCorner
                        Numpad{
                            id : numpadQML
                            anchors.centerIn: parent
                            onInput:{
                                if(preferencePage.password.turnSetter.value){
                                    preferencePage.password.setValue(val,preferencePage.password.insertList,pass.index);
                                    pass.index +=1;
                                    if(pass.index >= pass.lenght)
                                    pass.index=0;
                                }
                                else{
                                    preferencePage.password.setValue(val,preferencePage.password.reinsertList,ripass.index);
                                    ripass.index +=1;
                                    if(ripass.index >= ripass.lenght)
                                    ripass.index=0;
                                }
                            }
                            onDeleteNumber: {
                                if(preferencePage.password.turnSetter.value){
                                    preferencePage.password.deleteValue(preferencePage.password.insertList,pass.index)
                                    pass.index -=1;
                                    if(pass.index <= 0)
                                    pass.index=0;
                                }
                                else{
                                    preferencePage.password.deleteValue(preferencePage.password.reinsertList,ripass.index)
                                    ripass.index -=1;
                                    if(ripass.index <= 0)
                                    ripass.index=0;
                                }
                            }
                            onCheck: {
                                timer.start()
                                preferencePage.password.checkCreation()
                                pass.index = 0;
                                ripass.index = 0;
                            }
                        }
                        RoburIconButton{
                            visible : {
                                if(navigator.currentSection == SectionEnum.SECTION_SETTINGS.sectionLabel)
                                return true
                                else
                                return false
                            }
                            isButton: true
                            icon: "qrc:/icons/arrow.png"
                            rotation : 180
                            anchors.bottom: parent.bottom
                            anchors.right: parent.right
                            anchors.bottomMargin: 8
                            anchors.rightMargin: 32
                            onClicked: {
                                navigator.navigate(SubSectionEnum.SUBSUBSECTION_SETTINGS_DDCSS_MC_CONFIGURATION.subSectionLabel)
                            }
                        }
                    }
                }
            }
        }
    }
