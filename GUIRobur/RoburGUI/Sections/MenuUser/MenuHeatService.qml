import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    GridLayout{
        anchors.fill: parent
        columns: 3
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/water-temperature.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_WATER_SETPOINT.subSectionLabel);
                nameSubSection:   qsTr("title_settings") + " " +  qsTr("setpoint")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_WATER_SETPOINT.subSectionLabel
                type : "SNOW"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //TODO CORREGGERE ICONA
                //icon : "qrc:/icons/wave.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel);
                nameSubSection:  qsTr("abilita_fasce_acqua")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel
                type :  pipesController.isTwoPipe.value ?  "SNOW_SUN" :"SNOW"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/drop-silhouette.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_ACS_BASE.subSectionLabel);
                nameSubSection: qsTr("impostaz_acqua_acs_base")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_ACS_BASE.subSectionLabel
                type : "ACS_BASE"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/water-tap.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_ACS_SEPARABLE.subSectionLabel);
                nameSubSection:  qsTr("impostaz_acqua_acs_separabile")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_ACS_SEPARABLE.subSectionLabel
                type : "ACS_SEPARABILE"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/termometer.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_SETTINGS.subSectionLabel);
                nameSubSection: qsTr("impostaz_ambiente")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_SETTINGS.subSectionLabel
                type : "SNOW"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/exthermometer.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_EXTERN_SETTINGS.subSectionLabel);
                nameSubSection:  qsTr("impostaz_ambiente_esterno")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_EXTERN_SETTINGS.subSectionLabel
                type : "SNOW"
            }
        }
    }
}
