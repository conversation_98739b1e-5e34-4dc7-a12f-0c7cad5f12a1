import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Navigation"
import "../../../Testing"
import "../ComponentsUserSection"
Rectangle {
    id : root
    radius: 4
    Layout.fillWidth: true
    Layout.fillHeight: true
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    SwitcherType
    {
        visible:pipesController.isTwoPipe.value
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 8
    }
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Item{
            Layout.preferredWidth: 150
            Layout.preferredHeight: 30
            RoburSwitch {
                id: generalSwitch
                width: 60
                height: 30
                isChecked : ambient.enabledTimeSlotSetPoint.value
                anchors.verticalCenter: parent.verticalCenter
                anchors.left: parent.left
                anchors.leftMargin: 8
                firstOptionText: qsTr("abilitato")
                secondOptionText: qsTr("disabled")
                onClicked: {
                    ambient.enabledTimeSlotSetPoint.value = !ambient.enabledTimeSlotSetPoint.value
                }
                activeBaseColor: Style.colors.darkGray
                inactiveBaseColor: Style.colors.darkGray
                pivotDisabledBorderColor: Style.colors.darkGray
                pivotUpBorderColor: Style.colors.white
                pivotUpColor: Style.colors.darkOrange
                colorTextFistOption : Style.colors.orange
                // colorTextSecondOption: Style.colors.darkGray
                //activeBaseColor: Style.colors.darkGray
                // inactiveBaseColor: Style.colors.darkGray
                // pivotDisabledBorderColor: Style.colors.darkGray
                // pivotUpBorderColor: Style.colors.darkGray
                // pivotUpColor: Style.colors.darkOrange
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:  ambient.enabledTimeSlotSetPoint.value
            radius : Style.dimensions.radiusCorner
            color: ambient.timeSlotView.value ? Style.colors.lightGray : Style.colors.thinGray
            opacity: ambient.timeSlotView.value ? 0.7 : 0.4
            border.color:ambient.timeSlotView.value ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("set_timeslot")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: ambient.timeSlotView.value ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    ambient.timeSlotView.value = true
                    ambient.timeSetPoinView.value = false
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:  ambient.enabledTimeSlotSetPoint.value
            radius : Style.dimensions.radiusCorner
            color:  ambient.timeSetPoinView.value ? Style.colors.lightGray : Style.colors.thinGray
            opacity:  ambient.timeSetPoinView.value ? 0.7 : 0.4
            border.color: ambient.timeSetPoinView.value ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("setpoint") +  " " + qsTr("differential")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:   ambient.timeSetPoinView.value ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    ambient.timeSlotView.value = false
                    ambient.timeSetPoinView.value = true
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:pipesController.isTwoPipe.value
            SwitcherType
            {
                visible:pipesController.isTwoPipe.value
                anchors.centerIn: parent
            }
        }
    }
    RowLayout {
        id : dataMod
        anchors.top: containerSection.bottom
        anchors.topMargin: 16
        anchors.left: containerSection.left
        anchors.right: containerSection.right
        height: Style.dimensions.iconSize
        enabled:  ambient.enabledTimeSlotSetPoint.value
        visible: ambient.enabledTimeSlotSetPoint.value
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: Style.dimensions.iconSize
            visible: ambient.timeSlotView.value
            RoburIconButton {
                id: copy
                isButton: true
                width: Style.dimensions.iconSize
                height: Style.dimensions.iconSize
                anchors.centerIn: parent
                icon: "qrc:/icons/copy-file-icon.png"
                onClicked: {
                    ambient.copyPreviusDay();
                }
            }
        }
        Item{
            Layout.preferredWidth: Style.dimensions.iconSize
            Layout.preferredHeight: Style.dimensions.iconSize
            visible: ambient.timeSlotView.value
            RoburIconButton {
                id: down
                isButton: true
                anchors.centerIn: parent
                icon: "qrc:/icons/down-arrow.png"
                width: Style.dimensions.iconSize
                height: Style.dimensions.iconSize
                onClicked: {
                    ambient.saveCurrentList();
                    ambient.currentIndex-= 1;
                    ambient.timer()
                }
                transform: Rotation {
                    origin.x: down.width / 2
                    origin.y: down.height / 2
                    angle: 90
                }
            }
        }
        Item {
            Layout.fillWidth: true
            Layout.preferredHeight: Style.dimensions.iconSize
            visible: ambient.timeSlotView.value
            RoburText {
                id: name
                text: qsTr(ambient.currentDay.value)
                font.pixelSize: Style.dimensions.fontBigSize + 6
                font.capitalization: Font.AllUppercase
                wrapMode: Text.Wrap
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.preferredWidth: Style.dimensions.iconSize
            Layout.preferredHeight: Style.dimensions.iconSize
            visible: ambient.timeSlotView.value
            RoburIconButton {
                id: up
                isButton: true
                anchors.centerIn: parent
                icon: "qrc:/icons/down-arrow.png"
                width: Style.dimensions.iconSize
                height: Style.dimensions.iconSize
                onClicked: {
                    ambient.saveCurrentList();
                    ambient.currentIndex+= 1;
                    ambient.timer()
                }
                transform: Rotation {
                    origin.x: up.width / 2
                    origin.y: up.height / 2
                    angle: -90
                }
            }
        }
    }
    AmbientSettingsTimeSlots{
        visible:  ambient.timeSlotView.value &&  ambient.enabledTimeSlotSetPoint.value
        anchors.top:  dataMod.bottom
        anchors.topMargin: 8
        anchors.bottom: parent.bottom
        anchors.left: containerSection.left
        anchors.right: containerSection.right
    }
    AmbientSettingsSetPoints{
        visible:  ambient.timeSetPoinView.value &&  ambient.enabledTimeSlotSetPoint.value
        anchors.top:  containerSection.bottom
        anchors.bottom: parent.bottom
        anchors.left: containerSection.left
        anchors.right: containerSection.right
    }
}
