import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
import "../"
Rectangle {
    id : root
    property bool activeSection1Label : true
    anchors.fill: parent
    property var _setpoint
    radius: 4
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("default_water_setpoint")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                }
            }
        }
        SwitcherType
        {
            id : switcher
            Layout.preferredHeight: switcher.height
            Layout.preferredWidth: switcher.width
            visible:pipesController.isTwoPipe.value
        }
    }
    RowLayout{
        id : box
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini{
            label: qsTr("default_water_setpoint")
            activeColor: Style.colors.orange
            candidateValue:  preferencePage.temperatureBox.value ? root._setpoint.value  : ( root._setpoint.value * 9/5) + 32
            //candidateValue: root._setpoint.value
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            enableTick: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            btnSize: 50
            anchors.horizontalCenter:  parent.horizontalCenter
            onDownClicked: {
                root._setpoint.value -=0.1;
            }
            onUpClicked: {
                root._setpoint.value += 0.1;
            }
            onClicked: {
                root.active = !root.active
                root.test()
            }
        }
    }
}
