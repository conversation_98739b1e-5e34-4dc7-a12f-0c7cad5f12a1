import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Navigation"
import "../ComponentsUserSection"
Rectangle {
    id : root
    radius: 4
    property int sizeTimeSetter: 40
    Layout.fillWidth: true
    Layout.fillHeight: true
    ColumnLayout{
        anchors.topMargin: 16
        anchors.bottomMargin:  16
        anchors.fill: parent
        anchors.leftMargin: 16
        Item{
            Layout.fillWidth: true
            Layout.fillHeight: true
            RowLayout {
                anchors.fill: parent
                spacing: 8
                Item{
                    Layout.preferredHeight: 45
                    Layout.fillWidth: true
                    RowLayout
                    {
                        anchors.fill: parent
                        RoburIconButton {
                            id: down
                            isButton: true
                            Layout.preferredWidth: 45
                            Layout.preferredHeight: 45
                            icon: "qrc:/icons/down-arrow.png"
                            onClicked: {
                                waterBandPage.previusDay();
                            }
                            transform: Rotation {
                                origin.x: down.width / 2
                                origin.y: down.height / 2
                                angle: 90
                            }
                        }
                        RoburText {
                            id: name
                            text: qsTr( waterBandPage.currentDay.value)
                            font.pixelSize: Style.dimensions.fontBigSize + 6
                            font.capitalization: Font.AllUppercase
                            horizontalAlignment: Text.AlignHCenter
                            wrapMode: Text.Wrap
                            Layout.alignment: Qt.AlignVCenter
                            Layout.fillWidth: true
                        }
                        RoburIconButton {
                            id: up
                            isButton: true
                            Layout.preferredWidth: 45
                            Layout.preferredHeight: 45
                            icon: "qrc:/icons/down-arrow.png"
                            onClicked: {
                                waterBandPage.nextDay();
                            }
                            transform: Rotation {
                                origin.x: up.width / 2
                                origin.y: up.height / 2
                                angle: -90
                            }
                        }
                    }
                }
                Item{
                    Layout.fillWidth: true
                    Layout.preferredHeight: 45
                    RoburIconButton
                    {
                        id : text
                        writable: true
                        labelTextButton : qsTr("copia_giorno")
                        anchors.left: parent.left
                        anchors.right: parent.right
                        isButton: true
                        height: 45
                        onClicked: {
                            waterBandPage.copyPreviusDay()
                        }
                    }
                }
                Item{
                    Layout.preferredWidth: 160
                }
            }
        }
        Item{
            Layout.fillWidth: true
            Layout.preferredHeight: 240 + listView.spacing + listView.anchors.topMargin
            ListView {
                id: listView
                anchors.fill: parent
                anchors.topMargin: 16
                orientation: ListView.Vertical
                spacing: 16
                interactive: true
                clip : true
                flickableDirection: Flickable.VerticalFlick
                boundsBehavior: Flickable.StopAtBounds
                model: waterBandPage.model
                ScrollBar.vertical: ScrollBar {
                    snapMode: ScrollBar.SnapOnRelease
                    policy: ScrollBar.AlwaysOn
                }
                delegate: Rectangle {
                    required property var modelData
                    required property var index
                    implicitHeight: 120
                    implicitWidth: listView.width
                    ModelWaterBands{
                        onHour:  modelData.onHour
                        onMinute :  modelData.onMinutes
                        offHour:  modelData.offHour
                        offMinute :  modelData.offMinutes
                        temperature: modelData.temperatureSetter
                        onTextBand : modelData.onTextSetter.value
                        offTextBand : modelData.offTextSetter.value
                        onOff : modelData.onOffSetter
                        temperatureText: qsTr("temperature")
                    }
                }
            }
        }
    }
}
