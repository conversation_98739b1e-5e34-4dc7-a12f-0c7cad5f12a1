import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
import "../"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int leftMarginAnchor: 16
    property int rightMarginAnchor: 16
    Component {
        id: delegate
        Item{
            id: wrapper
            anchors.fill: parent
            visible: PathView.isCurrentItem ? true : false
            required property var modelData
            required property var index
            ModelWaterSetpoint{
                _setpoint: modelData.setpointValue
            }
        }
    }
    PathView {
        id : pathview
        anchors.fill: parent
        model: setpointPage.filter
        delegate: delegate
        interactive : false
        path: Path {
            id: path
            startX:root.width/2; startY: root.height/2
            PathQuad {
                x: root.width;
                y: root.height/2;
                controlX: root.width;
                controlY: root.height/2
            }
            PathQuad {
                x: 0;
                y: root.height/2;
                controlX: 0;
                controlY: root.height/2}
            }
            Component.onCompleted: {
                setpointPage.filter.filterString = navigator.pendingType
            }
        }
    }
