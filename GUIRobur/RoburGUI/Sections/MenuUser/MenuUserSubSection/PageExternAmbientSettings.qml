import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Rectangle {
    id : root
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    property bool activeSection3Label: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius : Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("setpoint")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                    root.activeSection3Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("differential")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                    root.activeSection3Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection3Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection3Label ? 0.7 : 0.4
            border.color:root.activeSection3Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("setpoint temperatura esterna") + " " + qsTr("temperatura_esterna_filtrata")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.activeSection3Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = false;
                    root.activeSection3Label = true;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:pipesController.isTwoPipe.value
            SwitcherType
            {
                visible:pipesController.isTwoPipe.value
                anchors.centerIn: parent
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        RoburCounterMini{
            label : qsTr("setpoint")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
            onDownClicked: {
                ambientExternSettingPage.setpoint.value -=0.1;
            }
            onUpClicked: {
                ambientExternSettingPage.setpoint.value+= 0.1;
            }
        }
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
    RowLayout{
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        RoburCounterMini{
            label : qsTr("differential")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ?ambientExternSettingPage.differential.value   : (ambientExternSettingPage.differential.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
            onDownClicked: {
                ambientExternSettingPage.differential.value-=0.1;
            }
            onUpClicked: {
                ambientExternSettingPage.differential.value+= 0.1;
            }
        }
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
    RoburText{
        id : idText
        text:  qsTr("reset")+" "+qsTr("temperatura_esterna_filtrata") + " : "
        anchors.top:containerSection.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        width: 400
        anchors.leftMargin: 16
        height: Style.dimensions.iconSize * 2
        visible:root.activeSection3Label
    }
    RoburIconButton{
        icon : "qrc:/icons/restart.png"
        anchors.verticalCenter: idText.verticalCenter
        anchors.left: idText.right
        visible:  root.activeSection3Label
        isButton: true
        onClicked: {
        }
    }
    RowLayout{
        id : container
        visible: root.activeSection3Label
        anchors.top:idText.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini{
            id : thirdBox
            label: qsTr("setpoint temperatura esterna")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ?ambientExternSettingPage.externalTemperature.value   : (ambientExternSettingPage.externalTemperature.value * 9/5) + 32
            btnSize :  Style.dimensions.cnButtonRoburCounter
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            changeValue: false
            widthToOccupe: container.width
            heightToOccupe: container.height
            onDownClicked: {
                ambientExternSettingPage.externalTemperature.value-=0.1;
            }
            onUpClicked: {
                ambientExternSettingPage.externalTemperature.value+= 0.1;
            }
        }
        RoburCounterMini{
            id : fouthBox
            label : qsTr("temperatura_esterna_filtrata")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.externalTemperatureFilter.value   : (ambientExternSettingPage.externalTemperatureFilter.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            changeValue: false
            widthToOccupe: container.width
            heightToOccupe: container.height
            onDownClicked: {
                ambientExternSettingPage.externalTemperatureFilter.value-=0.1;
            }
            onUpClicked: {
                ambientExternSettingPage.externalTemperatureFilter.value+= 0.1;
            }
        }
    }
}
