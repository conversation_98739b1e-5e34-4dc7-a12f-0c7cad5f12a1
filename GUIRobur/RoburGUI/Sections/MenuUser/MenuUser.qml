import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    RowLayout{
        anchors.fill: parent
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_PREFERENCE.subSectionLabel);
                nameSubSection:  qsTr("preferenze")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_PREFERENCE.subSectionLabel
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:pipesController.isTwoPipe.value
            SectionNavigator{
                //icon : "qrc:/icons/snowflake.png"
                nameSubSection:  qsTr("altre_impostazioni")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_HEATSERVICE.subSectionLabel
                type : "SNOW"
                onChange: {
                    navigator.pendingType = "SNOW"
                    setpointPage.filter.filterString = "SNOW"
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:!pipesController.isTwoPipe.value
            SectionNavigator{
                icon : "qrc:/icons/snowflake.png"
                nameSubSection:  qsTr("impostaz_servizio_riscaldamento")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_HEATSERVICE.subSectionLabel
                type : "SNOW"
                onChange: {
                    navigator.pendingType = "SNOW"
                    setpointPage.filter.filterString = "SNOW"
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible:!pipesController.isTwoPipe.value
            SectionNavigator{
                icon : "qrc:/icons/sun.png"
                nameSubSection:  qsTr("impostaz_servizio_condizionamento")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_CONDITIONSERVICE.subSectionLabel
                type : "SUN"
                onChange: {
                    navigator.pendingType = "SUN"
                    setpointPage.filter.filterString = "SUN"
                }
            }
        }
    }
}
