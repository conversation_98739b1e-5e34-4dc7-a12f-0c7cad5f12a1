import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    GridLayout{
        anchors.fill: parent
        columns: 2
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                //icon : "qrc:/icons/cold-water.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_CONDIWATERSETTNGS.subSectionLabel);
                nameSubSection:   qsTr("title_settings") + " " +  qsTr("setpoint")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_WATER_SETPOINT.subSectionLabel
                type: "SUN"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                // icon : "qrc:/icons/wave.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel);
                nameSubSection:  qsTr("abilita_fasce_acqua")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel
                type: "SUN"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_CONDMBIENT.subSectionLabel);
                //icon : "qrc:/icons/termometer.png"
                nameSubSection:  qsTr("impostaz_ambiente")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_CONDMBIENT.subSectionLabel
                type: "SUN"
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_USERMENU_CONDAMBIENTEXT.subSectionLabel);
                //icon : "qrc:/icons/exthermometer.png"
                nameSubSection:  qsTr("impostaz_ambiente_esterno")
                subsectionToGo: SubSectionEnum.SUBSECTION_USERMENU_CONDAMBIENTEXT.subSectionLabel
                type: "SUN"
            }
        }
    }
}
