import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../MenuUser"
import "../MenuUser/MenuUserSubSection"
ColumnLayout{
    spacing: 0
    anchors.fill: parent
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU.subSectionLabel
        sourceComponentLazy : MenuUser{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_PREFERENCE.subSectionLabel
        sourceComponentLazy : PagePreferences{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_HEATSERVICE.subSectionLabel
        sourceComponentLazy : MenuHeatService{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_CONDITIONSERVICE.subSectionLabel
        sourceComponentLazy : MenuCoolService{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_WATER_SETPOINT.subSectionLabel
        sourceComponentLazy : PageWaterSetpoint{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel
        sourceComponentLazy : PageWaterBandsSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_ACS_BASE.subSectionLabel
        sourceComponentLazy : PageACSBase{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_ACS_SEPARABLE.subSectionLabel
        sourceComponentLazy : PageACSSeparable{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_SETTINGS.subSectionLabel
        sourceComponentLazy : PageAmbientSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_EXTERN_SETTINGS.subSectionLabel
        sourceComponentLazy : PageExternAmbientSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_SILENCE.subSectionLabel
        sourceComponentLazy : PageTODOSilenceMod{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_CONDMBIENT.subSectionLabel
        sourceComponentLazy : PageAmbientSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_CONDAMBIENTEXT.subSectionLabel
        sourceComponentLazy : PageExternAmbientSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_CONDSILENCE.subSectionLabel
        sourceComponentLazy : PageTODOSilenceMod{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
}
