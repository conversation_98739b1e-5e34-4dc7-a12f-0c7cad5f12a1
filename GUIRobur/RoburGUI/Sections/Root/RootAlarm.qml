import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../Alarms"
Item {
    Layout.fillHeight: true
    Layout.fillWidth: true
    RowLayout{
        anchors.fill: parent
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_ALARMS.subSectionLabel
            sourceComponentLazy : PageAlarm{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
}
