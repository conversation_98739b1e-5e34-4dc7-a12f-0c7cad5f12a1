import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../Components"
import "../Information"
RowLayout{
    anchors.fill: parent
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_INFODASHBOARD.subSectionLabel
        sourceComponentLazy : MenuInformation{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_INFO_EVENTHISTORY.subSectionLabel
        sourceComponentLazy : PageHistoryEvent{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_INFO_DDCINFOIMPLANT.subSectionLabel
        sourceComponentLazy : PageInformationDDCImplant{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel
        sourceComponentLazy : PageDataMachine{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_INFO_INFOMACHINE.subSectionLabel
        sourceComponentLazy : PageInformationMachine{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel
        sourceComponentLazy : PageCoolMachineStatus{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel
        sourceComponentLazy : PageHeatMachineStatus{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
}
