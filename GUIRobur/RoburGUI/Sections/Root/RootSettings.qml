import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../Home"
import "../Information"
import "../MenuSettings"
import "../MenuSettings/SubSectionDDC"
import "../MenuSettings/SubSectionDDC/ConfigurationSection"
import "../MenuSettings/SubSectionImplant"
import "../MenuSettings/SubSectionImplant/MenuBase"
import "../MenuSettings/SubSectionImplant/MenuBase/ConfigurationService"
import "../MenuSettings/SubSectionImplant/MenuSeparable"
import "../MenuSettings/SettingsComponents"
import "../MenuUser"
ColumnLayout{
    spacing: 0
    anchors.fill: parent
    //FRONT PAGE
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS.subSectionLabel
        sourceComponentLazy : MenuSettings{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCCONTROLPLANT.subSectionLabel
        sourceComponentLazy : PageDDCImplantControl{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
        sourceComponentLazy : PageAgree{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel
        sourceComponentLazy : PageMachineManagement{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_GAPH_REGOLATION_MODE.subSectionLabel
        sourceComponentLazy : PageGAHRegulationMode{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel
        sourceComponentLazy : PageHeatMachineStatus{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel
        sourceComponentLazy : PageCoolMachineStatus{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    //DDC PAGE
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_DDCBOARD.subSectionLabel
        sourceComponentLazy : MenuDDC{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_INSTALL_CONFIGURATION.subSectionLabel
        sourceComponentLazy : PageBlockInstallConfiguration{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_IMPLANTBOARD.subSectionLabel
        sourceComponentLazy : MenuImplant{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    //DDC SUBSECTION
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTING_DDCS_CHECK_CONFIGURATION_MACHINE.subSectionLabel
        sourceComponentLazy : PageCheckConfiguration{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_COMUNICATION_PARAMETER.subSectionLabel
        sourceComponentLazy : PageComunicationParameters{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_ID_SETTINGS.subSectionLabel
        sourceComponentLazy : ConfigurationIdSetting{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_RESET_FABBRIC.subSectionLabel
        sourceComponentLazy : PageFactoryResetSoftwareUpdate{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_THIRD_PART_GENERATOR.subSectionLabel
        sourceComponentLazy : PageThirdPartCategory{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_MACHINE_CONFIGURATION.subSectionLabel
        sourceComponentLazy : PageInitMachineConfiguration{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    //Configurazione macchina discovery etc...
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_SETTINGS_DDCSS_MC_START.subSectionLabel
        sourceComponentLazy : ConfigurationIdSetting{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_SETTINGS_DDCSS_MC_CONFIGURATION.subSectionLabel
        sourceComponentLazy : ConfigurationMachine{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_SETTINGS_DDCSS_MC_ACS.subSectionLabel
        sourceComponentLazy : ConfigurationMachineACSBase{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_SETTINGS_DDCSS_MC_ACSEPARABILE.subSectionLabel
        sourceComponentLazy : ConfigurationMachineACSSeparabile{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    // subsection impianto base e separabile e configurazione
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_CIRCOLAR_EXIT_ALARM.subSectionLabel
        sourceComponentLazy : PageCircolarExitAlarms{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_IMPLANT_SEPARABLE.subSectionLabel
        sourceComponentLazy : MenuSeparable{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_IMPLANT_BASE.subSectionLabel
        sourceComponentLazy : MenuBase{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    //here we are in a subsubsetion of subestion imapianto base
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_CONFIGURATION_ACS_BASE.subSectionLabel
        sourceComponentLazy : ConfigurationACSBase{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_WATERBOLERPOWER.subSectionLabel
        sourceComponentLazy : PageDifferentialWaterInitialPowerBolierPowerImplant{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    ConditionalLoader{
        when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_ERRCONGTEMP.subSectionLabel
        sourceComponentLazy : PageErrorConfigurationTempetarure{}
        Layout.fillHeight: true
        Layout.fillWidth: true
    }
    // ConditionalLoader{
        //     when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_SELSERVACSS.subSectionLabel
        //     sourceComponentLazy : SelectionServiceACS{}
        //     Layout.fillHeight: true
        //     Layout.fillWidth: true
        // }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_PREUSEMACHINE.subSectionLabel
            sourceComponentLazy : PagePriorityUseMachine{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_RBOXCIRCOLARRIT.subSectionLabel
            sourceComponentLazy : PageRBOXCircularDelay{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_VALVEPARAM.subSectionLabel
            sourceComponentLazy : PageInversionParametersValve{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_CONFIGURATION_SERVICE.subSectionLabel
            sourceComponentLazy : MenuConfigurationService{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_PARAMREGOLATION.subSectionLabel
            sourceComponentLazy : PageConfirmRegolationParemeterBase{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_BASEIMPLANT_PARAMREGROBUR.subSectionLabel
            sourceComponentLazy : PageParameterRegolationBase{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        //========================================================
        //SUBSUB SECTION OF heatConfigurationHeatingService
        //========================================================
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_AGREEDDCRBOX.subSectionLabel
            sourceComponentLazy : ServiceAgreeDCCRbox{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_TERMOSETPOINT.subSectionLabel
            sourceComponentLazy : ServiceTemoSetPoint{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_LIMPOWERMAXHEAT.subSectionLabel
            sourceComponentLazy : ServiceCurva{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_PARAMCURVACLIMATICA.subSectionLabel
            sourceComponentLazy : ServiceLimitPower{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        //====================================================================
        //here we are in a subsubsetion of SUBSECTION_IMPLANT_SEPARABLE
        //===============================================================
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_ACSSETMAXWAT.subSectionLabel
            sourceComponentLazy : PageACSWaterSetpointWaterDifferential{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_POWIMPLANTINIT.subSectionLabel
            sourceComponentLazy : PageInitialPowerNominalPowerImplant{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SEPARABLESER_RBOXVFASEVALVE.subSectionLabel
            sourceComponentLazy : PageRBOXCircularDelaySwitchingPhaseValveParameters{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGOLATION.subSectionLabel
            sourceComponentLazy : PageConfirmParameterRegolationSeparable{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGROBUR.subSectionLabel
            sourceComponentLazy : PageDDCSetParameter{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGTHIRD.subSectionLabel
            sourceComponentLazy : PageThirdCategoriesSetParameter{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        // ConditionalLoader{
            //     when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_PREFERENCE.subSectionLabel
            //     sourceComponentLazy : PagePreferences{}
            //     Layout.fillHeight: true
            //     Layout.fillWidth: true
            // }
        }
