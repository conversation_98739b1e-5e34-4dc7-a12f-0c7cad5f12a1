import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Navigation"
import "../../Testing"
import "../Home"
import "../Information"
import "../MenuSettings"
import "../MenuUser/MenuUserSubSection"
Item {
    id : root
    Layout.fillHeight: true
    Layout.fillWidth: true
    Text {
        anchors.centerIn: parent
    }
    ColumnLayout{
        spacing: 0
        anchors.fill: parent
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_DASHBOARD.subSectionLabel
            sourceComponentLazy : PageHome{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel
            sourceComponentLazy : PageHeatMachineStatus{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel
            sourceComponentLazy : PageCoolMachineStatus{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_SETTINGS_DDCCONTROLPLANT.subSectionLabel
            sourceComponentLazy : PageDDCImplantControl{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
            sourceComponentLazy : PageAgree{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_ACS_BASE.subSectionLabel
            sourceComponentLazy : PageACSBase{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_ACS_SEPARABLE.subSectionLabel
            sourceComponentLazy : PageACSSeparable{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_AMBIENT_SETTINGS.subSectionLabel
            sourceComponentLazy : PageWaterBandsSettings{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_WATERBANDS_SETTINGS.subSectionLabel
            sourceComponentLazy : PageAmbientSettings{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        ConditionalLoader{
            when: navigator.currentSubSection === SubSectionEnum.SUBSECTION_USERMENU_WATER_SETPOINT.subSectionLabel
            sourceComponentLazy : PageWaterSetpoint{}
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
}
