import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../"
import "../../Components"
import "../../Testing"
import "../../Navigation"
import "InformationComponents"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    Rectangle {
        id : titleEvent
        height: Style.dimensions.heightZoneElement - 10
        width: root.width
        radius: 4
        anchors.top:  root.top
        anchors.topMargin: 4
        RowLayout{
            anchors.fill: parent
            spacing: 8
            Item{
                Layout.fillHeight: true
                Layout.preferredWidth: 4
            }
            Item {
                Layout.fillHeight: true
                Layout.preferredWidth: Style.dimensions.cnIcon
                RoburText {
                    text: qsTr("label_2char_ID")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                MouseArea{
                    anchors.fill: parent
                    onClicked: {
                    }
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("macchina")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("seriale_scheda")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("revisione_hw_ddc")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("revisione_fw_ddc")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("servizio")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("codice")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("matricola")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("servizio")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("codice")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("matricola")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.preferredWidth: 4
            }
        }
    }
    Item {
        anchors.top:  titleEvent.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        ListView {
            id: listView
            anchors.fill: parent
            interactive: true
            spacing: 6
            clip: true
            model: idSortFilterProxyInfoPage
            boundsBehavior: Flickable.StopAtBounds
            orientation: ListView.Vertical
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight: Style.dimensions.heightZoneElement - 10
                implicitWidth: root.width
                color: Style.colors.white
                radius: 4
                ModelInformationMachine{
                    idMacchine : modelData.idMacchine
                    idModelMachine :  modelData.modelMachine.value
                    idSerialNumber : modelData.serialNumber.value
                    idRevisiontHW : modelData.revisionHW.value
                    idRevisiontSW : modelData.revisionSW.value
                    idServiceRobur : modelData.serviceRobur.value
                    idCode : modelData.code.value
                    idMatricola : modelData.matricola.value
                    idServiceThirdpart : modelData.serviceThirdpart.value
                    idCodeThirdpart : modelData.codeThirdpart.value
                    idMatricolaThirdpart : modelData.matricolaThirdpart.value
                }
                Component.onCompleted: {
                    idSortFilterProxyInfoPage.sortDescending()
                }
            }
        }
    }
}
