import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../Components"
import "../../"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    RowLayout{
        anchors.fill: parent
        ColumnLayout
        {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_INFO_DDCINFOIMPLANT.subSectionLabel);
                    nameSubSection : qsTr("informazioni_ddc_e_impianto")
                    subsectionToGo: SubSectionEnum.SUBSECTION_INFO_DDCINFOIMPLANT.subSectionLabel
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_INFO_INFOMACHINE.subSectionLabel);
                    nameSubSection : qsTr("informazioni_macchine")
                    subsectionToGo: SubSectionEnum.SUBSECTION_INFO_INFOMACHINE.subSectionLabel
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_INFO_EVENTHISTORY.subSectionLabel);
                    nameSubSection : qsTr("storico_eventi")
                    subsectionToGo: SubSectionEnum.SUBSECTION_INFO_EVENTHISTORY.subSectionLabel
                }
            }
        }
        ColumnLayout
        {
            Layout.fillHeight: true
            Layout.fillWidth: true
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel);
                    nameSubSection :qsTr("dati_macchine")
                    subsectionToGoFirst: SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel
                    subsectionToGoSecond:SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel
                    isTwoTypeService : false
                    subsectionToGo : SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel
                    onFirstPress:
                    {
                        dataImplantPage.filter.filterString = "SNOW"
                    }
                    onSecondPress:   {
                        dataImplantPage.filter.filterString = "SUN"
                    }
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel);
                    nameSubSection : qsTr("title_units_status")
                    subsectionToGoFirst: SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel
                    subsectionToGoSecond: SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel
                    //isTwoTypeService : pipesController.isTwoPipe.value
                    isTwoTypeService: false
                    //subsectionToGo : SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel
                    onFirstPress:
                    {
                        hsmHeatingPage.filter.filterString = "SNOW"
                    }
                    onSecondPress:  {
                        hsmConditioningPage.filter.filterString = "SUN"
                    }
                }
            }
        }
    }
}
