import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../"
import "../../Components"
import "../../Navigation"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int distance: 4
    GridLayout{
        anchors.fill: parent
        columns : 2
        rows : 2
        Rectangle {
            Layout.preferredHeight: Style.dimensions.heightElements
            Layout.fillWidth: true
            radius: 4
            RoburText{
                text: qsTr("ddc_config")
                anchors.fill: parent
                anchors.leftMargin: 16
            }
        }
        Rectangle {
            Layout.preferredHeight: Style.dimensions.heightElements
            Layout.fillWidth: true
            radius: 4
            RoburText{
                text: qsTr("impianto")
                anchors.fill: parent
                anchors.leftMargin: 16
            }
        }
        Rectangle {
            id : frame
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius: 4
            Flickable {
                id : flickable
                anchors.fill: parent
                contentWidth: frame.width
                contentHeight: col.children.length * Style.dimensions.heightElements + distance * (col.children.length - 1)
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                ScrollIndicator.vertical: ScrollIndicator {
                    parent: flickable.parent
                    anchors.top: flickable.top
                    anchors.bottom: flickable.bottom
                    anchors.left: flickable.right
                    width: 11
                    active: true
                    visible: false  // Show it always
                    opacity: 1.0   // Fully visible
                    // Prevent Flickable from deactivating it
                    onActiveChanged: {
                        if (!active) active = true;
                    }
                }
                ColumnLayout{
                    id : col
                    anchors.margins: 16
                    anchors.fill: parent
                    spacing: 16
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName: qsTr("id_rete_can")
                            labelValue: ddcInfoPlant.idRete.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            id : idTemp
                            labelName: qsTr("temperatura_ambiente")
                            labelValue: ddcInfoPlant.temperature.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            id : idTempEsterna
                            labelName: qsTr("temperatura_esterna")
                            labelValue: ddcInfoPlant.externTemperature.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:  qsTr("Alim_Volt")
                            labelValue: ddcInfoPlant.supplyVolt.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:qsTr("seriale")
                            labelValue: ddcInfoPlant.serialNumber.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName: qsTr("revisione_hw_ddc")
                            labelValue: ddcInfoPlant.revisionHW.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:  qsTr("revisione_fw_ddc")
                            labelValue: ddcInfoPlant.revisionFW.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName: qsTr("revisione_bootloader_ddc")
                            labelValue: ddcInfoPlant.revisionBootL.value
                            isSetTexValueSpace : false
                            setTextValueSpace : 100
                            borderData : 4
                        }
                    }
                }
            }
        }
        Rectangle {
            id : frame2
            radius: 4
            Layout.fillHeight: true
            Layout.fillWidth: true
            Flickable {
                id : flickable2
                anchors.fill: parent
                contentWidth: frame2.width
                contentHeight: col2.children.length * Style.dimensions.heightElements + distance * (col2.children.length - 1)
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                ScrollIndicator.vertical: ScrollIndicator {
                    parent: flickable2.parent
                    anchors.top: flickable2.top
                    anchors.bottom: flickable2.bottom
                    anchors.left: flickable2.right
                    width: 11
                    active: true
                    visible: true  // Show it always
                    opacity: 1.0   // Fully visible
                    // Prevent Flickable from deactivating it
                    onActiveChanged: {
                        if (!active) active = true;
                    }
                }
                ColumnLayout{
                    id : col2
                    anchors.margins: 16
                    anchors.fill: parent
                    spacing: 16
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:  qsTr("numero_macchine")
                            labelValue: ddcInfoPlant.nMachine.value
                            setTextValueSpace : 100
                            isSetTexValueSpace : false
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:qsTr("numero_moduli_caldo")
                            labelValue: ddcInfoPlant.nHotModule.value
                            setTextValueSpace : 100
                            borderData : 4
                            isSetTexValueSpace : false
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName: qsTr("numero_moduli_freddo")
                            labelValue: ddcInfoPlant.nFreezeModule.value
                            setTextValueSpace : 100
                            isSetTexValueSpace : false
                            borderData : 4
                        }
                    }
                    Item{
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        RoburDataViewer{
                            labelName:qsTr("numero_moduli_separabili")
                            labelValue: ddcInfoPlant.nSeparableModule.value
                            setTextValueSpace : 100
                            isSetTexValueSpace : false
                            borderData : 4
                        }
                    }
                }
            }
        }
    }
}
