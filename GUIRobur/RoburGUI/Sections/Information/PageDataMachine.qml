import QtQuick 2.16
import QtQuick.Controls 2.16
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Navigation"
import "InformationComponents"
Item {
    id: root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int leftMarginAnchor: 16
    property int rightMarginAnchor: 16
    Component {
        id: delegate
        Item{
            id: wrapper
            anchors.fill: parent
            visible: PathView.isCurrentItem ? true : false
            required property var modelData
            required property var index
            required property var machineType
            ModelDataImplant{
                _implantid:modelData.id
                _implantstatusOnOff:modelData.statusOnOff
                _implantmachineType:machineType
                _implantalarms:modelData.alarms
                _implantunitSeparable:modelData.unitSeparable
                statisticCounterList : modelData.statisticCountersmodel
                analogOtherDataValueList: modelData.analogOtherDataValuesmodel
                analogTemperatureValueList: modelData.analogTemperatureValuesmodel
                onGoToLefthPathIndex: {
                    pathview.decrementCurrentIndex()
                }
                onGoToRigthPathIndex: {
                    pathview.incrementCurrentIndex()
                }
            }
        }
    }
    PathView {
        id : pathview
        anchors.fill: parent
        model: dataImplantPage.filter
        delegate: delegate
        interactive : false
        path: Path {
            id: path
            startX:root.width/2; startY: root.height/2
            PathQuad {
                x: root.width;
                y: root.height/2;
                controlX: root.width;
                controlY: root.height/2
            }
            PathQuad {
                x: 0;
                y: root.height/2;
                controlX: 0;
                controlY: root.height/2}
            }
        }
    }
