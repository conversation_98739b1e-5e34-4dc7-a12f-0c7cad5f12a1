import QtQuick 2.16
import QtQuick.Controls 2.16
import QtQuick.Layouts
import QtQuick.Effects
import "../../../"
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    id: root
    anchors.fill: parent
    signal goToRigthPathIndex();
    signal goToLefthPathIndex();
    property var statisticCounterList
    property var analogTemperatureValueList
    property var analogOtherDataValueList
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    property bool activeSection3Label: false
    property int leftMarginAnchor: 4
    property int rightMarginAnchor: 4
    property var _implantid;
    property var _implantstatusOnOff;
    property var _implantmachineType;
    property var _implantalarms;
    property var _implantunitSeparable;
    property var _implantoperatingRemainingMonths;
    Item{
        id : idTitle
        anchors.top:  root.top
        anchors.topMargin: 8
        //anchors.horizontalCenter: root.horizontalCenter
        anchors.left: parent.left
        anchors.right: parent.right
        height: Style.dimensions.cnButton
        RowLayout{
            anchors.fill: parent
            Rectangle{
                Layout.fillHeight: true
                Layout.preferredWidth: 300
                radius: 4
                RoburIconButton{
                    id : idLeft
                    icon: "qrc:/icons/arrow.png"
                    isButton: true
                    anchors.left: parent.left
                    anchors.leftMargin : 8
                    anchors.verticalCenter: parent.verticalCenter
                    height: 36
                    width: height
                    onClicked: {
                        root.goToLefthPathIndex()
                    }
                }
                Item{
                    id: idId
                    height: Style.dimensions.cnButton
                    anchors.left: idLeft.right
                    anchors.right: idRight.left
                    anchors.margins: 8
                    RoburDataViewer {
                        labelName: qsTr("label_2char_ID")
                        labelValue: root._implantid.value
                        anchors.centerIn: parent
                    }
                }
                RoburIconButton{
                    id : idRight
                    icon: "qrc:/icons/arrow.png"
                    isButton: true
                    rotation: 180
                    anchors.right: parent.right
                    anchors.rightMargin:8
                    anchors.verticalCenter: parent.verticalCenter
                    height: 36
                    width: height
                    onClicked: {
                        root.goToRigthPathIndex()
                    }
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburDataViewer{
                    labelName: qsTr("macchina")
                    labelValue: qsTr(root._implantmachineType.value)
                    isLabelNameTakeSameSpaceLabelValue: false
                    labelNameSpace: 100
                    anchors.fill: parent
                }
            }
        }
    }
    Item{
        id : idContainer
        anchors.topMargin: 8
        anchors.top: idTitle.bottom
        anchors.left: root.left
        anchors.right: root.right
        height: Style.dimensions.cnButton
        ColumnLayout{
            anchors.fill: parent
            Item{
                Layout.preferredHeight: Style.dimensions.heightConfiguration
                Layout.fillWidth: true
                RowLayout{
                    anchors.fill: parent
                    Rectangle{
                        Layout.preferredHeight: Style.dimensions.heightConfiguration
                        Layout.fillWidth: true
                        color : Style.colors.white
                        radius : Style.dimensions.radiusCorner
                        RoburDataViewer{
                            labelName: qsTr("stato")
                            labelValue: root._implantstatusOnOff.value
                            anchors.fill: parent
                            anchors.leftMargin: 4
                        }
                    }
                    Rectangle{
                        Layout.preferredHeight: Style.dimensions.heightConfiguration
                        Layout.fillWidth: true
                        color : Style.colors.white
                        radius : Style.dimensions.radiusCorner
                        RoburDataViewer{
                            labelName: qsTr("allarme")
                            labelValue: root._implantalarms.value
                            anchors.fill: parent
                            anchors.leftMargin: 4
                        }
                    }
                    Rectangle{
                        visible: dataImplantPage.filter.filterString === "SUN" ? false : true
                        //visible : root._implantunitSeparable.value
                        Layout.preferredHeight:  Style.dimensions.heightConfiguration
                        Layout.fillWidth: true
                        color : Style.colors.white
                        radius : Style.dimensions.radiusCorner
                        RoburDataViewer {
                            id : idMachineUStext
                            labelName: qsTr("unit_type") +" "+ qsTr("separabile")
                            labelValue: root._implantunitSeparable.value ? qsTr("yes") : qsTr("no")
                            anchors.fill: parent
                            anchors.leftMargin: 4
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }
            }
        }
    }
    RowLayout{
        id : containerSection
        anchors.top: idContainer.bottom
        anchors.topMargin: 16
        anchors.left: idContainer.left
        anchors.right: idContainer.right
        height: Style.dimensions.cnButton
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: Style.colors.white
            opacity: root.activeSection1Label ? 1.0 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("temperature")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                    root.activeSection3Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            //color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            color: Style.colors.white
            opacity: root.activeSection2Label ? 1.0 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("altri_dati")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                    root.activeSection3Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            //color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            color: Style.colors.white
            opacity: root.activeSection3Label ? 1.0 : 0.4
            border.color:root.activeSection3Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("title_statistics")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.activeSection3Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = false;
                    root.activeSection3Label = true;
                }
            }
        }
    }
    Item {
        id: frame
        anchors.top: containerSection.bottom
        anchors.topMargin: 16
        anchors.left: root.left
        anchors.right: root.right
        anchors.bottom: root.bottom
        //height:  Style.dimensions.heightZoneElement * 3
        visible: root.activeSection1Label
        ListView
        {
            id: listView
            anchors.fill: parent
            orientation: ListView.Vertical
            interactive: true
            spacing: 4
            property bool timerToggle: false
            clip : true
            flickableDirection: Flickable.VerticalFlick
            boundsBehavior: Flickable.StopAtBounds
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            model: root.analogTemperatureValueList
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight: Style.dimensions.heightZoneElement
                implicitWidth: listView.width
                radius: 4
                RoburDataViewer
                {
                    labelName: modelData.key
                    labelValue:  modelData.value
                    labelUnit:  modelData.measureUnit
                    isSetTexValueSpace : false
                    setTextValueSpace : 100
                    anchors.fill: parent
                }
            }
        }
    }
    Item {
        id: frame1
        anchors.topMargin: 16
        anchors.top: containerSection.bottom
        anchors.left: root.left
        anchors.right: root.right
        anchors.bottom: root.bottom
        visible: root.activeSection2Label
        ListView
        {
            id: listView1
            anchors.fill: parent
            orientation: ListView.Vertical
            interactive: true
            spacing: 4
            property bool timerToggle: false
            clip : true
            flickableDirection: Flickable.VerticalFlick
            boundsBehavior: Flickable.StopAtBounds
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            model: root.analogOtherDataValueList
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight:Style.dimensions.heightZoneElement
                implicitWidth: listView.width
                radius: 4
                RoburDataViewer
                {
                    labelName: qsTr(modelData.key)
                    labelValue:  modelData.value
                    labelUnit:  modelData.measureUnit
                    isSetTexValueSpace : false
                    setTextValueSpace : 100
                }
            }
        }
    }
    Item {
        id: frame2
        anchors.top: containerSection.bottom
        anchors.topMargin: 16
        anchors.left: root.left
        anchors.right: root.right
        anchors.bottom: root.bottom
        visible: root.activeSection3Label
        ListView
        {
            id: listView2
            anchors.fill: parent
            orientation: ListView.Vertical
            interactive: true
            spacing: 4
            property bool timerToggle: false
            clip : true
            flickableDirection: Flickable.VerticalFlick
            boundsBehavior: Flickable.StopAtBounds
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            model: root.statisticCounterList
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight:Style.dimensions.heightZoneElement
                implicitWidth: listView.width
                radius: 4
                RoburDataViewer
                {
                    labelName: qsTr(modelData.key)
                    labelValue:  modelData.value
                    labelUnit:  modelData.measureUnit
                    isSetTexValueSpace : false
                    setTextValueSpace : 100
                }
            }
        }
    }
}
