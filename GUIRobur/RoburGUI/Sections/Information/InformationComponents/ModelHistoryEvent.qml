import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
import "../../.."
Item {
    id : root
    property string idId
    property string idDate
    property string idTime
    property string idUnitType
    property string idEvent
    property string idDescription
    property bool idStatusBool
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 1
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idDate
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idTime
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 2
            RoburText{
                text : root.idId
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idUnitType)
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idEvent)
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idDescription)
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                anchors.fill: parent
                horizontalAlignment: Text.AlignHCenter
                text : root.idStatusBool ? qsTr("on") : qsTr("off")
            }
        }
    }
}
