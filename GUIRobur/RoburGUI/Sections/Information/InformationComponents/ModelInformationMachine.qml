import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    id : root
    property string idMacchine
    property string idModelMachine
    property string idSerialNumber
    property string idRevisiontHW
    property string idRevisiontSW
    property string idServiceRobur
    property string idCode
    property string idMatricola
    property string idServiceThirdpart
    property string idCodeThirdpart
    property string idMatricolaThirdpart
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 4
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.cnIcon
            RoburText{
                text : root.idMacchine
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idModelMachine)
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idSerialNumber
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idRevisiontHW
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idRevisiontSW
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                icon: root.idServiceRobur
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idCode
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idMatricola
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                icon: root.idServiceThirdpart
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idCodeThirdpart
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idMatricolaThirdpart
                anchors.centerIn: parent
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 4
        }
    }
}
