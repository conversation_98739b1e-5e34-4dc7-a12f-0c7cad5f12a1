import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../"
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    id : root
    property string idId
    property string idMachine
    property string idStatus
    property string idAlarms
    property var idStateFlux
    property var idFlamOn
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 8
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize
            RoburText{
                text : root.idId
                anchors.centerIn: parent
                width: parent.width
                height: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                minimumPointSize: Style.dimensions.fontDefaultSize
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idMachine
                anchors.centerIn: parent
                width: parent.width
                height: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                minimumPointSize: Style.dimensions.fontDefaultSize
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idStatus
                anchors.centerIn: parent
                width: parent.width
                height: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                minimumPointSize: Style.dimensions.fontDefaultSize
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text: root.idAlarms
                anchors.centerIn: parent
                width: parent.width
                height: parent.width
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                minimumPointSize: Style.dimensions.fontDefaultSize
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburCheckbox{
                enabled: false
                controlCheck: root.idStateFlux.value
                isTexted: false
                width: parent.width /2
                anchors.centerIn: parent
                height: parent.height
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburCheckbox{
                enabled: false
                controlCheck: root.idFlamOn.value
                isTexted: false
                width: parent.width /2
                anchors.centerIn: parent
                height: parent.height
            }
        }
    }
}
