import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../.."
import "../../Components"
import "InformationComponents"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    Rectangle {
        id : rootTitle
        width: root.width
        height: Style.dimensions.heightZoneElement - 10
        anchors.top: parent.top
        radius: 4
        Layout.alignment: Qt.AlignTop
        RowLayout{
            anchors.fill: parent
            spacing: 4
            Item{
                Layout.fillHeight: true
                Layout.preferredWidth: 8
            }
            Item {
                Layout.fillHeight: true
                Layout.preferredWidth: Style.dimensions.iconSize
                RoburText {
                    text: qsTr("label_2char_ID")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    anchors.centerIn: parent
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText{
                    text: qsTr("macchina")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("separabile")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text:  qsTr("stato")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("allarme")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("flussostato_on")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("RICHIESTA ACS")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("fiamma_on")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
    Item {
        id : listModel
        anchors.top:  rootTitle.bottom
        anchors.topMargin: 8
        anchors.left: root.left
        anchors.right: root.right
        anchors.bottom: root.bottom
        ListView {
            id: listView
            anchors.topMargin: 8
            anchors.fill: parent
            orientation: ListView.Vertical
            spacing: 4
            boundsBehavior: Flickable.StopAtBounds
            clip : true
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            model: hsmHeatingPage.filter
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight: Style.dimensions.heightZoneElement - 10
                implicitWidth: root.width
                color: Style.colors.white
                radius: 4
                ModelHeatMachineStatus{
                    idId : modelData.id.value
                    idMachine : qsTr(modelData.machine.value)
                    idStatus : modelData.status.value
                    idAlarms : modelData.alarms.value
                    idStateFlux : modelData.stateflux
                    idSeparable : modelData.separable
                    idAskACS : modelData.askACS
                    idFlamOn : modelData.flame
                }
            }
        }
    }
}
