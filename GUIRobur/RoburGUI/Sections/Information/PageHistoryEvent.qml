import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import QtQuick
import "../../"
import "../../Components"
import "../../Navigation"
import "InformationComponents"
Item {
    id : root
    property bool filteringMod: true
    property bool filterMoreRecent: false
    property bool filterLessRecent: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int filterId
    property int reset
    signal upClicked
    signal downClicked
    ColumnLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillWidth: true
            Layout.preferredHeight:  Style.dimensions.heightZoneElement * 2
            RowLayout{
                anchors.fill: parent
                spacing: 8
                Item{
                    id : filter
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    ColumnLayout
                    {
                        anchors.fill: parent
                        spacing: 8
                        Item {
                            id: name
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            RoburDataViewer{
                                id : idID
                                labelName:  qsTr("filtro_ricerca_per_id")
                                labelValue: historiPage.filter.filterString
                            }
                        }
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            radius: 4
                            RoburIconButton {
                                id: upButton
                                icon: "qrc:/icons/down-arrow.png"
                                rotation: 180
                                isButton: true
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.left: parent.left
                                anchors.leftMargin: 4
                                onClicked: {
                                    root.filterId += 1
                                    historiPage.filter.filterString = root.filterId
                                }
                                onTick:
                                {
                                    root.filterId += 1
                                    historiPage.filter.filterString = root.filterId
                                }
                            }
                            RoburIconButton {
                                id: downButton
                                icon: "qrc:/icons/down-arrow.png"
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.left: upButton.right
                                isButton: true
                                anchors.leftMargin: 16
                                onClicked: {
                                    root.filterId -= 1
                                    historiPage.filter.filterString = root.filterId
                                }
                                onTick:
                                {
                                    root.filterId -= 1
                                    historiPage.filter.filterString = root.filterId
                                }
                            }
                            RoburIconButton {
                                id: deleteFilter
                                //visible: filter.isActive
                                //firstColor:  Style.colors.white
                                isButton: true
                                anchors.verticalCenter: parent.verticalCenter
                                anchors.right:  parent.right
                                writable: true
                                width: idID.width / 2
                                anchors.rightMargin: 4
                                labelTextButton:  qsTr("elimina_filtro")// : qsTr("filtro_ricerca_per_id")
                                onClicked:
                                {
                                    root.filterLessRecent = false
                                    root.filterMoreRecent = false
                                    historiPage.filter.filterString = ""
                                    root.filterId = reset
                                    filterModel.currentFilterMode = 0;
                                }
                            }
                        }
                    }
                }
                Rectangle{
                    Layout.preferredWidth: 250
                    Layout.preferredHeight:  Style.dimensions.heightZoneElement* 2
                    radius: 4
                    ColumnLayout {
                        id : idFilterDate
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        width: parent.width
                        anchors.left: parent.left
                        anchors.leftMargin: 16
                        Item
                        {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            RoburIconButton {
                                id: moreRecent
                                icon: "qrc:/icons/arrow.png"
                                rotation: 90
                                anchors.verticalCenter: parent.verticalCenter
                                isSelected:  root.filterMoreRecent
                                secondColor: Style.colors.darkGray
                                onClicked: {
                                    root.filterMoreRecent = true
                                    root.filterLessRecent = false
                                    historiFilterModel.currentFilterMode = 1;
                                    historiFilterModel.setSortDescending()
                                }
                            }
                            RoburText{
                                anchors.verticalCenter: moreRecent.verticalCenter
                                anchors.left: moreRecent.right
                                anchors.leftMargin: 16
                                text: qsTr("piu_recenter")
                                color: root.filterMoreRecent ?  Style.colors.orange : Style.colors.darkGray
                            }
                        }
                        Item
                        {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            RoburIconButton {
                                id: lessRecent
                                icon: "qrc:/icons/arrow.png"
                                anchors.verticalCenter: parent.verticalCenter
                                isSelected:  root.filterLessRecent
                                secondColor: Style.colors.darkGray
                                rotation: 270
                                onClicked: {
                                    root.filterLessRecent = true
                                    root.filterMoreRecent = false
                                    historiFilterModel.currentFilterMode = 2;
                                    historiFilterModel.setSortAscending()
                                }
                            }
                            RoburText{
                                anchors.verticalCenter: lessRecent.verticalCenter
                                anchors.left: lessRecent.right
                                anchors.leftMargin: 16
                                text: qsTr("meno_recente")
                                color: root.filterLessRecent ?  Style.colors.orange : Style.colors.darkGray
                            }
                        }
                    }
                }
            }
        }
        AnimatedContainer
        {
            Layout.fillWidth:  true
            Layout.preferredHeight: Style.dimensions.heightZoneElement - 10
            isAnimated: false
            RowLayout{
                anchors.fill: parent
                spacing: 8
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        text: qsTr("title_date")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText{
                        text: qsTr("title_time")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.preferredWidth: Style.dimensions.iconSize * 2
                    RoburText {
                        text: qsTr("label_2char_ID")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        text: qsTr("unit_type")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        text: qsTr("evento")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        text: qsTr("descrizione_evento")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        text: qsTr("stato")
                        anchors.centerIn: parent
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
        AnimatedContainer{
            Layout.fillWidth:  true
            Layout.fillHeight: true
            isAnimated: false
            activeColor: Style.colors.no
            inactiveColor: Style.colors.no
            ListView {
                id: listView
                anchors.fill: parent
                orientation: ListView.Vertical
                interactive: true
                spacing: 4
                property bool timerToggle: false
                clip : true
                flickableDirection: Flickable.VerticalFlick
                boundsBehavior: Flickable.StopAtBounds
                ScrollBar.vertical: ScrollBar {
                    policy: ScrollBar.AlwaysOn
                    active: ScrollBar.AlwaysOn
                }
                model:(lessRecent.isSelected || moreRecent.isSelected)?  historiFilterModel :  historiPage.filter
                delegate: Rectangle {
                    required property var modelData
                    required property var index
                    implicitHeight: Style.dimensions.heightZoneElement - 10
                    implicitWidth: listView.width
                    color: Style.colors.white
                    radius: 4
                    ModelHistoryEvent{
                        idId : modelData.id
                        idDate : modelData.dateString
                        idTime : modelData.timeString
                        idUnitType : modelData.unitType.value
                        idEvent : modelData.event.value
                        idDescription : modelData.description.value
                        idStatusBool:   modelData.status.value
                    }
                }
            }
        }
    }
}
