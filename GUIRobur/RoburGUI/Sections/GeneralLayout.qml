import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../Testing"
import "../Components"
import "../Sections"
import "Alarms"
import "Blocks"
import "Home"
import "MenuSettings"
import "MenuUser"
import "Information"
import "../Navigation"
import "Root"
import ".."
Item {
    anchors.fill: parent
    id : root
    property bool changeSource: false
    property int loaderWidth: 800
    RowLayout {
        anchors.fill: parent
        spacing: 8
        //=============================================================
        // SIDE BAR NAVIGATION
        //=============================================================
        BarSideNavigation{z : 1}
        Item{
            Layout.fillWidth: true
            Layout.fillHeight:  true
            ColumnLayout {
                spacing: 8
                anchors.fill: parent
                //=============================================================
                // TOP BAR INFORMATION FOR ALL THE TIME
                //=============================================================
                BarTopInfo{
                    id : barInfo
                    Layout.topMargin: 4
                    Layout.alignment : Qt.AlignTop
                }
                //=============================================================
                // connecetti di animazione sperimentale aaaa
                //=============================================================
                Item{
                    id:loaderBox
                    Layout.fillWidth: true
                    Layout.fillHeight:  true
                    ConditionalLoader{
                        id : homeRoot
                        anchors.fill: parent
                        when: navigator.currentSection === SectionEnum.SECTION_HOME.sectionLabel
                        sourceComponentLazy : RootHome{}
                    }
                    ConditionalLoader{
                        id : informationRoot
                        anchors.fill: parent
                        when: navigator.currentSection === SectionEnum.SECTION_INFORMATION.sectionLabel
                        sourceComponentLazy : RootInformation{}
                    }
                    ConditionalLoader{
                        id : menuSettingRoot
                        anchors.fill: parent
                        when: navigator.currentSection === SectionEnum.SECTION_SETTINGS.sectionLabel
                        sourceComponentLazy : RootSettings{}
                    }
                    ConditionalLoader{
                        id : userMenuSettingsRoot
                        anchors.fill: parent
                        when: navigator.currentSection === SectionEnum.SECTION_USERMENU.sectionLabel
                        sourceComponentLazy : RootUser{}
                    }
                    ConditionalLoader{
                        id : alarmsRoot
                        anchors.fill: parent
                        when: navigator.currentSection === SectionEnum.SECTION_ALARMS.sectionLabel
                        sourceComponentLazy : RootAlarm{}
                    }
                    ConditionalLoader{
                        anchors.fill: parent
                        when: hourglass.commandLoading
                        sourceComponentLazy : SavePage{}
                    }
                }
                Item{
                    Layout.fillWidth: true
                    Layout.preferredHeight: 2
                    Layout.alignment: Qt.AlignBottom
                }
            }
        }
        Item{
            //side bar space
            Layout.fillHeight: true
            Layout.preferredWidth: 1
        }
    }
    Popup{
        id : popUp
        property string message
        modal: true
        focus: true
        width: Style.dimensions.widthBanner
        height: Style.dimensions.heightBanner
        x: (parent.width - width) / 2
        y: (parent.height - height) / 2
        closePolicy:Popup.CloseOnPressOutside
        background: Rectangle
        {
            color : Style.colors.no
        }
        Overlay.modal: Rectangle {
            color: "#aacfdbe7"
        }
        RoburMessage
        {
            message: popUp.message;
            anchors.fill: parent
        }
        // Connect to the Java signal
        Connections {
            target: popupController
            function onShowPopupMessage(message) {
                popUp.message = message;
                popUp.open();
            }
        }
    }
}
