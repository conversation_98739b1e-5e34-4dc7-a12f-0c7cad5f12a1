import QtQuick 2.16
import QtQuick.Controls 2.16
import QtQuick.Layouts
import QtQuick.Effects
import "../.."
import "../../Components"
import "../../Testing"
import "../Information/InformationComponents"
import "SettingsComponents"
Item {
    id: root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int leftMarginAnchor: 16
    property int rightMarginAnchor: 16
    Component {
        id: delegate
        Item{
            id: wrapper
            anchors.fill: parent
            visible: PathView.isCurrentItem ? true : false
            required property var modelData
            required property var index
            ModelAgree
            {
                _type : navigator.pendingType === "SNOW" ?qsTr("config_serv_riscald") : qsTr("config_serv_condizionam")
                _onlyView : agreePage.onlyView
                _enableGlobalOnOffGS : modelData.enableGlobalOnOffGS
                _enableGlobalWaterTimerGS : modelData.enableGlobalWaterTimerGS
                _enableCronoAmbientTempGS : modelData.enableCronoAmbientTempGS
                _enableExternalRequestGS : modelData.enableExternalRequestGS
                _enableOutdoorTempGS : modelData.enableOutdoorTempGS
                _enableRYRWOnOffSwitchGS : modelData.enableRYRWOnOffSwitchGS
                _enableInternalRequestGS : modelData.enableInternalRequestGS
                _enable_crono_weather_comp :modelData.enableCronoWeatherCompGS
                _agree : modelData.agree
                _monitor : modelData.monitor
                _plog : modelData.plog
            }
        }
    }
    PathView {
        id : pathview
        anchors.fill: parent
        model: agreePage.model
        delegate: delegate
        interactive : false
        path: Path {
            id: path
            startX:root.width/2; startY: root.height/2
            PathQuad {
                x: root.width;
                y: root.height/2;
                controlX: root.width;
                controlY: root.height/2
            }
            PathQuad {
                x: 0;
                y: root.height/2;
                controlX: 0;
                controlY: root.height/2}
            }
        }
    }
