import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    GridLayout{
        anchors.fill: parent
        columns: 3
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_ID_SETTINGS.subSectionLabel);
                nameSubSection:  qsTr("impostazione_id")
                subsectionToGo : ddcConfigured.value ?
                                     SubSectionEnum.SUBSECTION_DDCBOARD.subSectionLabel
                                   : SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_ID_SETTINGS.subSectionLabel
                onClickedSection: {
                    if(ddcConfigured.value)
                        popupController.triggerPopup("macchina configurata")
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_MACHINE_CONFIGURATION.subSectionLabel);
                nameSubSection:qsTr("title_heaters_configuration")
                subsectionToGo : SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_MACHINE_CONFIGURATION.subSectionLabel
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTING_DDCS_CHECK_CONFIGURATION_MACHINE.subSectionLabel);
                nameSubSection:  qsTr("check_config_macchine")
                subsectionToGo: SubSectionEnum.SUBSECTION_SETTING_DDCS_CHECK_CONFIGURATION_MACHINE.subSectionLabel
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_THIRD_PART_GENERATOR.subSectionLabel);
                nameSubSection:  qsTr("categ_generatore_terze_parti")
                subsectionToGo: SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_THIRD_PART_GENERATOR.subSectionLabel
                onClickedSection: {
                    checkConfigurationPage.filterThirdPart.filterString = "14"
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_COMUNICATION_PARAMETER.subSectionLabel);
                nameSubSection:  qsTr("Selezione parametri comunicazione")
                subsectionToGo: SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_COMUNICATION_PARAMETER.subSectionLabel
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_RESET_FABBRIC.subSectionLabel);
                nameSubSection:  qsTr("impostaz_fabbrica_agg_sw")
                subsectionToGo: SubSectionEnum.SUBSECTION_SETTINGS_DDCSS_RESET_FABBRIC.subSectionLabel
            }
        }
    }
}
