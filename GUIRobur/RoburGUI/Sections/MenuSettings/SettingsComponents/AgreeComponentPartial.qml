import QtQuick
import "../../.."
import "../../../Components"
Rectangle {
    id : root
    property string iconSource
    property string service
    property var boolSetter
    property bool visibleSetter
    property bool testBool : true
    property bool rightIsVisible: true
    property int durationAnimation: 250
    signal changeboolSetter(value : bool)
    width: 100
    height: 100
    Rectangle{
        width: 10
        radius: 100
        color : Style.colors.black
        height: width
        anchors.horizontalCenter: leftTrasparent.horizontalCenter
        anchors.verticalCenter:  topNoAnimated.verticalCenter
        z : 2
    }
    AgreePieceVertical{
        id : leftTrasparent
        anchors.left: root.left
        anchors.top: root.top
        anchors.bottom: root.bottom
        height: root.height
        color: Style.colors.no
    }
    AgreePieceVertical{
        id : left
        anchors.left: root.left
        anchors.bottom: root.bottom
        radius: 0
        height: leftTrasparent.height - top.rotation_width + 5
        color: Style.colors.black
        Rectangle{
            id :pointOnLeft
            width: 10
            radius: 100
            color :  root.boolSetter.value ? Style.colors.no : Style.colors.black
            height: width
            anchors.bottom:  left.top
            anchors.horizontalCenter: left.horizontalCenter
            states :[
            State {
                name: "black"
                when: !root.boolSetter.value
                PropertyChanges {
                    target: pointOnLeft
                    color : Style.colors.black
                }
            },
            State {
                name: "trasparante"
                when: root.boolSetter.value
                PropertyChanges {
                    target: pointOnLeft
                    color : Style.colors.no
                }
            }
            ]
            transitions: [
            Transition {
                from: "black"
                to: "trasparante"
                ColorAnimation {
                    from: Style.colors.black
                    to: Style.colors.no
                    easing.type: Easing.InOutQuad
                    duration:  root.durationAnimation
                }
            },
            Transition {
                from: "trasparante"
                to: "black"
                ColorAnimation {
                    from: Style.colors.no
                    to: Style.colors.black
                    easing.type: Easing.InOutQuad
                    duration:  root.durationAnimation
                }
            }
            ]
        }
    }
    AgreePieceHorizontal
    {
        id : topNoAnimated
        width_: 20
        color: root.rightIsVisible ? Style.colors.black : Style.colors.no
        anchors.right:  root.right
        anchors.top: root.top
        visible: false
    }
    Rectangle{
        width: 10
        radius: 100
        color : Style.colors.black
        height: width
        anchors.right:  topNoAnimated.left
        anchors.verticalCenter:  topNoAnimated.verticalCenter
    }
    AgreePieceHorizontalAnimated
    {
        id : top
        anchors.top: root.top
        anchors.left:  root.left
        activeService_width: root.width - 20
        rotation_width: root.width - 20
        active : root.boolSetter.value
    }
    Item{
        id : bottom
        anchors.bottom: root.bottom
        width: root.width
        height: 4
        AgreePieceHorizontal{
            anchors.left: parent.left
            anchors.right: button.left
        }
        AgreePieceHorizontal{
            radius: 0
            anchors.right: parent.right
            anchors.left: button.right
        }
        RoburIconButton
        {
            id : button
            anchors.centerIn: parent
            icon :root.iconSource
            isSelected:  root.boolSetter.value
            secondColor: Style.colors.darkGray
            onClicked: {
                if(!root.visibleSetter)
                {
                    root.boolSetter.value =! root.boolSetter.value
                    console.debug( root.boolSetter.value )
                }
            }
        }
        Text{
            id : idTextSetter
            text: root.service
            font.pointSize:  Style.dimensions.fontSmallSize
            font.bold: true
            anchors.top: button.bottom
            anchors.topMargin: 4
            anchors.horizontalCenter: button.horizontalCenter
            font.capitalization: Font.AllUppercase
            color: root.boolSetter.value ? Style.colors.orange : Style.colors.darkGray
        }
    }
    AgreePieceVertical{
        id : right
        visible: false
        anchors.right:bottom.right
        anchors.top: root.top
        anchors.bottom: root.bottom
    }
}
