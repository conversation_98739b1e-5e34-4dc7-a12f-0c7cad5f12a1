import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
Item {
    id: root
    anchors.fill: parent
    signal goToRigthPathIndex();
    signal goToLefthPathIndex();
    signal eslusion()
    property string idID
    property bool eslusioneMacchina
    property string lastMaintenance
    property string operatingTime
    property string months
    property bool isOffline
    signal callResetError;
    RowLayout{
        anchors.fill: parent
        Item
        {
            Layout.fillWidth: true
            Layout.fillHeight: true
            ColumnLayout{
                anchors.fill: parent
                Rectangle{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    radius: 4
                    RoburIconButton{
                        id : idLeft
                        icon: "qrc:/icons/arrow.png"
                        isButton: true
                        anchors.left: parent.left
                        anchors.leftMargin : 8
                        anchors.verticalCenter: parent.verticalCenter
                        onClicked: {
                            root.goToLefthPathIndex()
                        }
                    }
                    Item{
                        id: idId
                        height: Style.dimensions.cnButton
                        anchors.left: idLeft.right
                        anchors.right: idRight.left
                        anchors.margins: 8
                        anchors.verticalCenter: parent.verticalCenter
                        RoburDataViewer {
                            labelName: qsTr("label_2char_ID")
                            labelValue: root.idID
                            anchors.centerIn: parent
                        }
                    }
                    RoburIconButton{
                        id : idRight
                        icon: "qrc:/icons/arrow.png"
                        isButton: true
                        rotation: 180
                        anchors.right: parent.right
                        anchors.rightMargin: 8
                        anchors.verticalCenter: parent.verticalCenter
                        onClicked: {
                            root.goToRigthPathIndex()
                        }
                    }
                }
                Rectangle{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    radius: 4
                    color: checkEslusion.controlCheck ? Style.colors.orange : Style.colors.white
                    RoburCheckbox
                    {
                        id : checkEslusion
                        labelName:  qsTr("esclusione_macchina")
                        controlCheck: eslusioneMacchina
                        firstColor: Style.colors.white
                        secondColor: Style.colors.darkGray
                        anchors.left: parent.left
                        anchors.leftMargin: 8
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.right: idGoToInfo.left
                        onClicked: {
                        }
                    }
                    MouseArea
                    {
                        anchors.left: parent.left
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.right:  idGoToInfo.left
                        onClicked: {
                            root.eslusion()
                        }
                    }
                    RoburIconButton{
                        id : idGoToInfo
                        icon: "qrc:/icons/information.png"
                        isButton: checkEslusion.controlCheck
                        anchors.right: parent.right
                        anchors.rightMargin: 8
                        anchors.verticalCenter: parent.verticalCenter
                        //isPressed: root.isPressed
                        firstColor: Style.colors.white
                        secondColor:  checkEslusion.controlCheck ? Style.colors.white : Style.colors.gray
                        onClicked: {
                            if(navigator.pendingType == "SUN")
                            navigator.navigate(SubSectionEnum.SUBSECTION_HSMSTATUS_HEAT.subSectionLabel,"SUN");// info
                            else
                            navigator.navigate(SubSectionEnum.SUBSECTION_HSMSTATUS_CONDITION.subSectionLabel,"SNOW");// info
                        }
                    }
                }
                Item{
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    RoburRectangleBTN
                    {
                        nameBTN: qsTr("riarmo fiamma")
                        iconBTN: "qrc:/icons/fire.png"
                    }
                }
                Item{
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    opacity: root.isOffline ? 0.3 : 1
                    RoburRectangleBTN
                    {
                        nameBTN: qsTr("reset_errori")
                        iconBTN: "qrc:/icons/reset.png"
                        onClicked: {
                            root.callResetError()
                        }
                    }
                }
            }
        }
        Item
        {
            id : dataContainer
            Layout.fillWidth: true
            Layout.fillHeight: true
            ColumnLayout{
                anchors.fill: parent
                AnimatedContainer{
                    Layout.fillWidth: true
                    Layout.preferredHeight:Style.dimensions.heightZoneElement - 10
                    radius: 4
                    RoburText{
                        text: qsTr("interavalli di manutenzione")
                        width: parent.width
                        height: parent.height
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                AnimatedContainer
                {
                    id : firstContainer
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    widthToOccupe: dataContainer.width
                    heightToOccupe: dataContainer.height
                    isAnimated: true
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.white
                    RoburDataViewer{
                        id: interMananagementLast
                        labelName: qsTr("data ultima manutenzione")
                        labelValue: root.lastMaintenance
                        anchors.leftMargin:  4
                        isLabelNameTakeSameSpaceLabelValue : false
                        labelNameSpace : 200
                        isFoces : firstContainer.isActive
                        onFocus: Style.colors.orange
                    }
                }
                AnimatedContainer
                {
                    id : secondContainer
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    widthToOccupe: dataContainer.width
                    heightToOccupe: dataContainer.height
                    isAnimated: true
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.white
                    RoburDataViewer{
                        labelName: qsTr("ore di funzionamento")
                        labelValue: root.operatingTime
                        anchors.leftMargin:  4
                        isLabelNameTakeSameSpaceLabelValue : false
                        labelNameSpace : 200
                        isFoces : secondContainer.isActive
                        onFocus: Style.colors.orange
                    }
                }
                AnimatedContainer
                {
                    id : thirdContainer
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    widthToOccupe: dataContainer.width
                    heightToOccupe: dataContainer.height
                    isAnimated: true
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.white
                    RoburDataViewer{
                        labelName: qsTr("mesi di calendario")
                        labelValue: root.months
                        anchors.leftMargin:  4
                        isLabelNameTakeSameSpaceLabelValue : false
                        labelNameSpace : 200
                        isFoces: thirdContainer.isActive
                        onFocus: Style.colors.orange
                    }
                }
                AnimatedContainer
                {
                    id : fourthContainer
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    widthToOccupe: dataContainer.width
                    heightToOccupe: dataContainer.height
                    isAnimated: true
                    activeColor : Style.colors.orange
                    RoburCheckbox{
                        labelName: qsTr("Manutenzione eseguita?")
                        anchors.fill: parent
                        controlCheck : fourthContainer.isActive
                        firstColor:fourthContainer.isActive ? Style.colors.white :  Style.colors.darkGray
                        secondColor:fourthContainer.isActive ? Style.colors.darkGray :  Style.colors.gray
                    }
                }
            }
        }
    }
}
