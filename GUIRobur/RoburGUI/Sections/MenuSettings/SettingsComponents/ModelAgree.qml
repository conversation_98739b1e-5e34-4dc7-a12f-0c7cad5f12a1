import QtQuick
import "../../.."
import "../../../Components"
Rectangle {
    id : root
    property int width_switcher:70
    property int height_switcher:90
    property int distance_switcher: 30
    property string _type
    property var _onlyView
    property var _enableGlobalOnOffGS
    property var _enableGlobalWaterTimerGS
    property var _enableCronoAmbientTempGS
    property var _enable_crono_weather_comp
    property var _enableExternalRequestGS
    property var _enableOutdoorTempGS
    property var _enableRYRWOnOffSwitchGS
    property var _enableInternalRequestGS
    property var _agree
    property var _monitor
    property var _plog
    anchors.fill: parent
    Item{
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        height: 60
        RoburText{
            width: parent.width
            height: parent.height
            anchors.centerIn: parent
            text: qsTr(root._type)
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    AgreePieceHorizontal{
        id : start
        anchors.left: parent.left
        anchors.leftMargin: 4
        anchors.verticalCenter: parent.verticalCenter
        anchors.verticalCenterOffset: 20
        width_: 50
        Rectangle{
            anchors.left: parent.left
            anchors.verticalCenter: parent.verticalCenter
            height: 20
            radius: 100
            width: height
            color: Style.colors.black
        }
    }
    AgreeComponent{
        id : onoff
        iconSource:  "qrc:/icons/on-off-button.png"
        service: qsTr("on_off_generale")
        boolSetter:   root._enableGlobalOnOffGS
        visibleSetter: root._onlyView.value
        anchors.left: start.right
        anchors.top : start.top
        width: root.width_switcher
        height: root.height_switcher
    }
    AgreePieceHorizontal{
        id : separator1
        anchors.left: onoff.right
        anchors.verticalCenter: start.verticalCenter
        width_: 50
    }
    AgreePieceVertical{
        id : roburOff
        anchors.bottom: separator1.top
        anchors.left: separator1.horizontalCenter
        height: 150
    }
    AgreePieceHorizontal
    {
        id : connect
        anchors.top: roburOff.top
        anchors.left: roburOff.left
    }
    AgreeComponentPartial{
        id : enableExternalRequestGS
        iconSource:  "qrc:/icons/panel.png"
        service: qsTr("roburbox")
        visibleSetter: root._onlyView.value
        anchors.left: connect.right
        anchors.top : connect.top
        width: root.width_switcher
        height: 80
        boolSetter:   root._enableExternalRequestGS
    }
    AgreePieceHorizontal{
        id : separator7
        anchors.left: enableExternalRequestGS.right
        anchors.bottom: enableExternalRequestGS.bottom
        width_: 60
        anchors.right:  separator6.horizontalCenter
    }
    AgreeComponent{
        id : enableGlobalWaterTimerGS
        iconSource: "qrc:/icons/time.png"
        service: qsTr("fasce_acqua")
        boolSetter:   root._enableGlobalWaterTimerGS
        visibleSetter: root._onlyView.value
        anchors.left: separator1.right
        anchors.top : start.top
        width: root.width_switcher
        height: 80
    }
    AgreePieceHorizontal{
        id : separator2
        anchors.left: enableGlobalWaterTimerGS.right
        anchors.verticalCenter: start.verticalCenter
        width_: root.distance_switcher
    }
    AgreeComponent{
        id : cronotermostato
        iconSource: "qrc:/icons/thermometer.png"
        service: qsTr("chrono_thermostat")
        boolSetter:   root._enableCronoAmbientTempGS
        visibleSetter: root._onlyView.value
        anchors.left: separator2.right
        anchors.top : start.top
        width: root.width_switcher
        height: 100
        Item {
            anchors.horizontalCenter: cronotermostato.horizontalCenter
            anchors.top: parent.bottom
            anchors.topMargin: 36
            width : 120
            RoburCheckbox{
                controlCheck: _enable_crono_weather_comp.value
                labelName:  qsTr("working_mode_Wcmp")
                onClicked:
                {
                    _enable_crono_weather_comp.value = !_enable_crono_weather_comp.value
                }
            }
        }
    }
    AgreePieceHorizontal{
        id : separator3
        anchors.left: cronotermostato.right
        anchors.verticalCenter: start.verticalCenter
        width_: root.distance_switcher
    }
    AgreeComponent{
        id : temperaturaEsterna
        iconSource: "qrc:/icons/more.png"
        service: qsTr("ry_rw")
        boolSetter:   root._enableRYRWOnOffSwitchGS
        visibleSetter: root._onlyView.value
        anchors.left: separator3.right
        anchors.top : start.top
        width: root.width_switcher
        height: 85
    }
    AgreePieceHorizontal{
        id : separator4
        anchors.left: temperaturaEsterna.right
        anchors.verticalCenter: start.verticalCenter
        width_: root.distance_switcher
    }
    AgreeComponent{
        id : enableRYRWOnOffSwitchGS
        iconSource: "qrc:/icons/room-temperature.png"
        service: qsTr("temperatura_esterna")
        boolSetter:   root._enableOutdoorTempGS
        visibleSetter: root._onlyView.value
        anchors.left: separator4.right
        anchors.top : start.top
        width: root.width_switcher
        height: 110
    }
    AgreePieceHorizontal{
        id : separator5
        anchors.left: enableRYRWOnOffSwitchGS.right
        anchors.verticalCenter: start.verticalCenter
        width_: root.distance_switcher + 10
    }
    AgreeComponent{
        id : ddc
        iconSource:  "qrc:/icons/panel.png"
        service: qsTr("ddc")
        boolSetter: root._enableInternalRequestGS
        visibleSetter: root._onlyView.value
        rightIsVisible: false
        anchors.left: separator5.right
        anchors.top : start.top
        width: root.width_switcher
        height: 110
    }
    AgreePieceVertical{
        id : connectToRobuxBox
        anchors.top: separator7.top
        anchors.left: separator6.horizontalCenter
        anchors.bottom:separator6.bottom
    }
    AgreePieceHorizontal{
        id : separator6
        anchors.left: ddc.right
        anchors.bottom: ddc.bottom
        width_: 30
        Rectangle{
            anchors.left:  parent.right
            anchors.verticalCenter: parent.verticalCenter
            height: 20
            radius: 100
            width: height
            color: Style.colors.black
        }
    }
}
