import QtQuick
import "../../.."
import "../../../Components"
Rectangle{
    id : root
    property int width_
    property bool active : true
    property int durationAnimation: 250
    property int rotation_width : 70
    property int activeService_width : 80
    transformOrigin: Item.BottomLeft
    height: 4
    width: width_
    radius: Style.dimensions.radiusCorner
    color: Style.colors.black
    antialiasing: true
    Rectangle{
        id : pointSet
        width: 10
        radius: 100
        height: width
        anchors.verticalCenter: root.verticalCenter
        anchors.right: root.right
        states :[
        State {
            name: "activeService"
            when: root.active
            PropertyChanges {
                target: pointSet
                color : Style.colors.orange
            }
        },
        State {
            name: "inactiveService"
            when: !root.active
            PropertyChanges {
                target: pointSet
                color : Style.colors.black
            }
        }
        ]
        transitions: [
        Transition {
            from: "activeService"
            to: "inactiveService"
            ColorAnimation {
                from: Style.colors.orange
                to: Style.colors.black
                easing.type: Easing.InOutQuad
                duration:  root.durationAnimation
            }
        },
        Transition {
            from: "inactiveService"
            to: "activeService"
            ColorAnimation {
                from: Style.colors.black
                to: Style.colors.orange
                easing.type: Easing.InOutQuad
                duration:  root.durationAnimation
            }
        }
        ]
    }
    states: [
    State {
        name: "activeService"
        when: root.active
        PropertyChanges {
            target: root
            rotation: 90
        }
        PropertyChanges {
            target: root
            width_ : root.activeService_width
        }
        PropertyChanges {
            target: root
            color : Style.colors.orange
        }
    },
    State {
        name: "inactiveService"
        when: !root.active
        PropertyChanges {
            target: root
            rotation: 0
        }
        PropertyChanges {
            target: root
            width_ : root.rotation_width
        }
        PropertyChanges {
            target: root
            color : Style.colors.black
        }
    }
    ]
    transitions: [
    Transition {
        from: "activeService"
        to: "inactiveService"
        ParallelAnimation {
            NumberAnimation {
                target: root
                property: "rotation"
                from: 90
                to: 0
                easing.type: Easing.InOutQuad
                duration: root.durationAnimation
            }
            NumberAnimation {
                target: root
                property: "width_"
                from:  root.activeService_width
                to: root.rotation_width
                easing.type: Easing.InOutQuad
                duration: root.durationAnimation
            }
            ColorAnimation {
                from: Style.colors.orange
                to: Style.colors.black
                easing.type: Easing.InOutQuad
                duration:  root.durationAnimation
            }
        }
    },
    Transition {
        from: "inactiveService"
        to: "activeService"
        ParallelAnimation {
            NumberAnimation {
                target: root
                property: "rotation"
                from: 0
                to: 90
                easing.type: Easing.InOutQuad
                duration: root.durationAnimation
            }
            NumberAnimation {
                target: root
                property: "width_"
                from: root.rotation_width
                to:  root.activeService_width
                easing.type: Easing.InOutQuad
                duration: root.durationAnimation
            }
            ColorAnimation {
                from: Style.colors.black
                to: Style.colors.orange
                easing.type: Easing.InOutQuad
                duration:  root.durationAnimation
            }
        }
    }
    ]
}
