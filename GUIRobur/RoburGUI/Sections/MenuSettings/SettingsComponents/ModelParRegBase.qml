import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../.."
import "../../../Components"
Rectangle {
    radius: 4
    id : root
    property bool selection1: true
    property bool selection2: false
    anchors.fill: parent
    signal goToRigthPathIndex();
    signal goToLefthPathIndex();
    property var idID
    property var thermalPower
    property var ignitionPriority
    property var inhibitionTime
    property var miniumInhibitionTime
    property var numberOfStages
    property var coolingCapacity
    property var circulatorDelay
    property var isThirdPart
    //TODO rendere consistente il cambio di dati all'esterno, altrimenti non va nulla
    SwitcherType
    {
        visible:pipesController.isTwoPipe.value
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 8
    }
    RowLayout{
        id: idPlant
        anchors.top: parent.top
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        height: Style.dimensions.cnButton
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RowLayout{
                anchors.fill: parent
                Item{
                    Layout.fillHeight: true
                    Layout.preferredWidth:   Style.dimensions.cnButton
                    RoburIconButton{
                        icon: "qrc:/icons/arrow.png"
                        isButton: true
                        height:  Style.dimensions.cnButton
                        width: height
                        anchors.centerIn: parent
                        onClicked: {
                            root.goToLefthPathIndex()
                        }
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburDataViewer {
                        labelName: qsTr("label_2char_ID")
                        labelValue: root.idID.value
                        anchors.centerIn: parent
                        anchors.fill: parent
                        anchors.leftMargin: 32
                        anchors.rightMargin:  32
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.preferredWidth:   Style.dimensions.cnButton
                    RoburIconButton{
                        icon: "qrc:/icons/arrow.png"
                        isrotated: true
                        isButton: true
                        height: Style.dimensions.cnButton
                        width: height
                        anchors.centerIn: parent
                        onClicked: {
                            root.goToRigthPathIndex()
                        }
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }
    }
    RowLayout{
        id : selector
        anchors.top: idPlant.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection1 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection1 ? 0.7 : 0.4
            border.color:root.selection1 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text : root.isThirdPart.value ?
                qsTr("potenza_termica") + "\n" + qsTr("priorita") + "\n" +
                qsTr("accensione") + " " + qsTr("locking_time")
                :
                qsTr("potenza_frigorifera") + "\n" + qsTr("priorita") + "\n" +
                qsTr("accensione")  + "\n" + qsTr("locking_time")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.selection1 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = true
                    root.selection2 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text : root.isThirdPart.value ?  qsTr("min_run_time") + "\n" + qsTr("numero_stadi")
                :
                qsTr("min_run_time") + "\n" + qsTr("numero_stadi")+  "\n" + qsTr("ritardo_circolatore")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text : root.isThirdPart.value ?  qsTr("min_run_time") + "\n" + qsTr("numero_stadi")
                :
                qsTr("min_run_time") + "\n" + qsTr("numero_stadi")+  "\n" + qsTr("ritardo_circolatore")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                }
            }
        }
    }
    RowLayout
    {
        id : containerFirst
        visible: root.selection1
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("potenza_termica")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            visible: !root.isThirdPart.value
            candidateValue:  preferencePage.temperatureBox.value ? root.thermalPower.value   : (root.thermalPower.value * 9/5) + 32
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onDownClicked: {
                root.isThirdPart.value -=0.1;
            }
            onUpClicked: {
                root.isThirdPart.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("potenza_frigorifera")
            visible: root.isThirdPart.value
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? root.coolingCapacity.value   : (root.coolingCapacity.value * 9/5) + 32
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onDownClicked: {
                root.coolingCapacity.value -=0.1;
            }
            onUpClicked: {
                root.coolingCapacity.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("potenza_frigorifera")
            visible: root.isThirdPart.value
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? root.coolingCapacity.value   : (root.coolingCapacity.value * 9/5) + 32
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onDownClicked: {
                root.coolingCapacity.value -=0.1;
            }
            onUpClicked: {
                root.coolingCapacity.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("priorita") + " " + qsTr("accensione")
            candidateValue: root.ignitionPriority.value
            btnSize : 40
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onDownClicked: {
                root.ignitionPriority.value -=0.1;
            }
            onUpClicked: {
                root.ignitionPriority.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("locking_time")
            candidateValue: root.inhibitionTime.value
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onDownClicked: {
                root.inhibitionTime.value -=0.1;
            }
            onUpClicked: {
                root.inhibitionTime.value += 0.1;
            }
        }
    }
    RowLayout
    {
        id : containerSecond
        visible: root.selection2
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("min_run_time")
            candidateValue: root.miniumInhibitionTime.value
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDownClicked: {
                root.miniumInhibitionTime.value -=0.1;
            }
            onUpClicked: {
                root.miniumInhibitionTime.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("numero_stadi")
            candidateValue: root.numberOfStages.value
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDownClicked: {
                root.numberOfStages.value -=0.1;
            }
            onUpClicked: {
                root.numberOfStages.value += 0.1;
            }
        }
        RoburCounterMini{
            label : qsTr("ritardo_circolatore")
            candidateValue: root.circulatorDelay.value
            visible: root.isThirdPart
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDownClicked: {
                root.circulatorDelay.value -=0.1;
            }
            onUpClicked: {
                root.circulatorDelay.value += 0.1;
            }
        }
    }
}
