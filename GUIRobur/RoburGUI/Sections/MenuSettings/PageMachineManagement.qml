import QtQuick 2.16
import QtQuick.Controls 2.16
import QtQuick.Layouts
import QtQuick.Effects
import "../../../"
import "../../Components"
import "../../Navigation"
import "SettingsComponents"
Item {
    id: root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int leftMarginAnchor: 16
    property int rightMarginAnchor: 16
    Component {
        id: delegate
        Item{
            id: wrapper
            anchors.fill: parent
            visible: PathView.isCurrentItem ? true : false
            required property var modelData
            required property var index
            ModelMachineManagament{
                idID : modelData.canAdress
                eslusioneMacchina:  modelData.esclusionMachine.value
                operatingTime:  modelData.operatingTime.value
                lastMaintenance:  modelData.lastMaintenance.value
                months : modelData.months.value
                isOffline: modelData.isOffline.value
                onGoToLefthPathIndex: {
                    pathview.decrementCurrentIndex()
                }
                onGoToRigthPathIndex: {
                    pathview.incrementCurrentIndex()
                }
                onCallResetError:
                {
                    machineManagementPage.callResetError(index,modelData.id.value);
                }
                onEslusion:
                {
                    console.debug(modelData.esclusionMachine.value);
                    machineManagementPage.callEsclusione(index,modelData.id.value,!modelData.esclusionMachine.value);
                }
            }
        }
    }
    PathView {
        id : pathview
        anchors.fill: parent
        model: machineManagementPage.activeFilterProxyID.value ? machineManagementPage.filterProxyID : machineManagementPage.filter
        delegate: delegate
        interactive : false
        path: Path {
            id: path
            startX:root.width/2; startY: root.height/2
            PathQuad {
                x: root.width;
                y: root.height/2;
                controlX: root.width;
                controlY: root.height/2
            }
            PathQuad {
                x: 0;
                y: root.height/2;
                controlX: 0;
                controlY: root.height/2}
            }
        }
    }
