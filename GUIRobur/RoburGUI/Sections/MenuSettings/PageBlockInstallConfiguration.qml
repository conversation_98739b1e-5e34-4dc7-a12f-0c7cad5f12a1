import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../.."
import "../../Components"
import "../Blocks"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    NameSectionBackground{
        label: "installazione configurazioni"
        iconPath : "qrc:/icons/admin-panel.png"
    }
    RoburIconButton{
        id : seePass
        icon :  preferencePage.password.seePassword.value ?  "qrc:/icons/hide.png" : "qrc:/icons/view.png"
        isButton: true
        anchors.right: pass.left
        anchors.rightMargin: 16
        anchors.verticalCenter: pass.verticalCenter
        onClicked: {
            preferencePage.password.seePassword.value =! preferencePage.password.seePassword.value
        }
    }
    PasswordRobur{
        id : pass
        anchors.left: parent.left
        anchors.leftMargin: 96
        anchors.verticalCenter: root.verticalCenter
    }
    Numpad{
        id : numpadQML
        anchors.right: root.right
        anchors.rightMargin: 32
        anchors.verticalCenter: root.verticalCenter
        onInput:{
            preferencePage.password.setValue(val,pass.index);
            pass.index +=1;
            if(pass.index >= pass.lenght)
            pass.index=0;
        }
        onDeleteNumber: {
            preferencePage.password.deleteValue(preferencePage.password.list,pass.index)
            pass.index -=1;
            if(pass.index <= 0)
            pass.index=0;
        }
        onCheck : {
            if(preferencePage.password.checkValidate())
            {
                if(navigator.currentSection == SectionEnum.SECTION_SETTINGS.sectionLabel &&
                navigator.currentSubSection == SubSectionEnum.SUBSECTION_SETTINGS_INSTALL_CONFIGURATION.subSectionLabel)
                {
                    navigator.navigate(SubSectionEnum.SUBSECTION_SETTINGS.subSectionLabel);
                    navigator.changeSection(SectionEnum.SECTION_SETTINGS.sectionLabel);
                    preferencePage.password.unlockInstallConfiguration.value = true;
                    preferencePage.password.clear(preferencePage.password.list)
                }
                else{
                    console.debug("no state")
                }
            }
            else{
                console.debug("password no correct")
            }
        }
    }
}
