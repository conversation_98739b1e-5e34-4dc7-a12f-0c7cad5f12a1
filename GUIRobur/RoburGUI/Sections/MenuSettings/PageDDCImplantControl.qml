import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
/*
l colore verde indica che tutte le unità, che sono connesse all'impianto,
sono attive e disponibili;
colore arancione significa solo la metà o meno delle unità
che lavorano nell'impianto sono attive e disponibili;
colore rosso significa che più della metà delle unità
(o tutte) connesse all'impianto non è attivo, quindi probabilmente in allarme.
lightgreen  = verde
lightsalmon	= arancio
orangered   = rosso
*/
Item{
    id : root
    Layout.minimumHeight: Style.dimensions.heightZoneElement
    Layout.fillWidth: true
    Layout.fillHeight: true
    property bool seeConfirmPage: false
    GridLayout{
        rows :  pipesController.isTwoPipe.value ? 2 : 1
        columns :  pipesController.isTwoPipe.value ? 2 : 4
        anchors.fill: parent
        Item{
            visible:  pipesController.isTwoPipe.value
            Layout.fillHeight: true
            Layout.fillWidth: true
            NameSectionBackground{
                iconPath:  ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue === "cool" ?  "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                label: qsTr("heating_cooling_service_switch")
                isSelected: ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue === "cool"
            }
            RoburSwitch {
                id : commutazione
                isChecked: ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue === "cool"
                visible: true
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.margins: 16
                width: 60
                height: 30
            }
            MouseArea
            {
                anchors.fill: parent
                onClicked:
                {
                    root.seeConfirmPage = true
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            NameSectionBackground{
                iconPath:  "qrc:/icons/timer.png"
                label: qsTr("abilita_fasce_acqua")
                isSelected:firstSwitch.isChecked
            }
            RoburSwitch {
                id : firstSwitch
                visible: !ddcConfigure.enableWaterTimerPresentDisabledS.value
                isChecked:  ddcConfigure.enableWaterTimerGS.value
                secondOptionText: qsTr("no")
                firstOptionText:  qsTr("yes")
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.margins: 16
                width: 60
                height: 30
            }
            MouseArea
            {
                anchors.fill: parent
                enabled:  !ddcConfigure.enableWaterTimerPresentDisabledS.value
                onClicked:
                {
                    ddcConfigure.enableWaterTimerGS.value = !ddcConfigure.enableWaterTimerGS.value
                    //agreePage.waterBands.value = ddcConfigure.enableWaterTimerGS.value
                    firstSwitch.isChecked = ddcConfigure.enableWaterTimerGS.value
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            NameSectionBackground{
                iconPath : "qrc:/icons/thermometer.png"
                label:  qsTr("abilita_cronotermostato")
                isSelected:secondoSwitch.isChecked
            }
            RoburSwitch {
                id : secondoSwitch
                visible: !ddcConfigure.enableCronoAmbientTempPresentDisabledS.value
                isChecked: ddcConfigure.enableCronoAmbientTempGS.value
                secondOptionText:  qsTr("no")
                firstOptionText: qsTr("yes")
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.margins: 16
                width: 60
                height: 30
            }
            MouseArea
            {
                anchors.fill: parent
                enabled:  !ddcConfigure.enableCronoAmbientTempPresentDisabledS.value
                onClicked:
                {
                    ddcConfigure.enableCronoAmbientTempGS.value = ! ddcConfigure.enableCronoAmbientTempGS.value
                    console.debug(ddcConfigure.enableCronoAmbientTempGS.value )
                    //agreePage.chronothermostat.value = ddcConfigure.enableCronoAmbientTempGS.value
                    secondoSwitch.isChecked = ddcConfigure.enableCronoAmbientTempGS.value
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            NameSectionBackground{
                iconPath:  "qrc:/icons/temperature.png"
                label: qsTr("abilita_crono_curva_climatica")
                isSelected:thirdSwitch.isChecked
            }
            Item{
                visible: !ddcConfigure.enableCronoWeatherCompPresentDisabledS.value &&  !ddcConfigure.enableWaterTimerGS.value && !ddcConfigure.enableCronoAmbientTempGS.value
                anchors.right: parent.right
                anchors.left: parent.left
                anchors.bottom: thirdSwitch.bottom
                anchors.bottomMargin: 48
                anchors.topMargin: 64
                anchors.top: parent.top
                anchors.leftMargin: 4
                RoburText
                {
                    anchors.fill: parent
                    text: ddcConfigure.cronoWeatherCompTemperaturesGS.currentValue
                }
                GridLayout {
                    id : layout
                    rows: pipesController.isTwoPipe.value ? 2 : 4
                    columns: pipesController.isTwoPipe.value ? 2 : 1
                    anchors.fill: parent
                    property var options: ["chrono", "t1", "t2", "t3"]
                    Repeater {
                        model: layout.options
                        delegate: Item {
                            Layout.fillHeight: true
                            Layout.fillWidth: true
                            RoburCheckbox {
                                id: checkbox
                                visible: thirdSwitch.isChecked
                                labelName: {
                                    switch (modelData) {
                                        case "chrono": return qsTr("chrono_thermostat")
                                        case "t1": return qsTr("chrono_thermostat_t1")
                                        case "t2": return qsTr("chrono_thermostat_t2")
                                        case "t3": return qsTr("chrono_thermostat_t3")
                                        default: return modelData
                                    }
                                }
                                anchors.fill: parent
                                controlCheck: modelData === ddcConfigure.cronoWeatherCompTemperaturesGS.currentValue
                                firstColor: Style.colors.white
                                secondColor: Style.colors.gray
                                // Quando clicchi, aggiorni SOLO il model
                                onClicked: {
                                    if (ddcConfigure.cronoWeatherCompTemperaturesGS.currentValue !== modelData)
                                    {
                                        ddcConfigure.cronoWeatherCompTemperaturesGS.currentValue = modelData
                                        console.debug(modelData)
                                        console.debug(ddcConfigure.cronoWeatherCompTemperaturesGS.currentValue)
                                        console.debug(controlCheck ? "true" : "false");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            RoburSwitch {
                id : thirdSwitch
                visible: !ddcConfigure.enableCronoWeatherCompPresentDisabledS.value
                isChecked: ddcConfigure.enableCronoWeatherCompGS.value
                secondOptionText:  qsTr("no")
                firstOptionText: qsTr("yes")
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.margins: 16
                width: 60
                height: 30
                onClicked: {
                    ddcConfigure.enableCronoWeatherCompGS.value = !ddcConfigure.enableCronoWeatherCompGS.value
                }
            }
        }
    }
    Popup{
        modal: true
        focus: true
        anchors.centerIn: parent
        width: 500
        height: 250
        visible: root.seeConfirmPage
        closePolicy:Popup.NoAutoClose
        RoburConfirm
        {
            anchors.centerIn: parent
            onClose:
            {
                root.seeConfirmPage = false;
            }
        }
    }
}
