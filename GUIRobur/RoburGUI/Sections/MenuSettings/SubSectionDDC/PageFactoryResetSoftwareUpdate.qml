import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    ColumnLayout{
        anchors.fill: parent
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            radius: 4
            Item{
                id : text1
                width: parent.width
                height: 80
                anchors.bottom: container.top
                RoburText {
                    text: qsTr("msg_confirm_factory_reset")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item{
                id : container
                anchors.top: text1.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                height:  100
                RowLayout{
                    anchors.fill: parent
                    anchors.leftMargin: 32
                    anchors.rightMargin: 32
                    AnimatedConfirm
                    {
                        id : firstContainer
                        label :  qsTr("yes")
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        widthToOccupe: container.width
                        heightToOccupe: container.height
                        isAnimated: true
                    }
                    AnimatedConfirm
                    {
                        label :  qsTr("no")
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        widthToOccupe: container.width
                        heightToOccupe: container.height
                        isAnimated: true
                    }
                }
            }
        }
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            radius: 4
            Item{
                id : text2
                width: parent.width
                height: 80
                anchors.bottom: container.top
                RoburText {
                    text: qsTr("vuoi_aggiornare_sw_ddc")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item{
                id : container2
                anchors.top: text2.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                height:  100
                RowLayout{
                    anchors.fill: parent
                    anchors.leftMargin: 32
                    anchors.rightMargin: 32
                    AnimatedConfirm
                    {
                        label :  qsTr("yes")
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        widthToOccupe: container2.width
                        heightToOccupe: container2.height
                        isAnimated: true
                    }
                    AnimatedConfirm
                    {
                        label :  qsTr("no")
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        widthToOccupe: container2.width
                        heightToOccupe: container2.height
                        isAnimated: true
                    }
                }
            }
        }
    }
}
