import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
import "../../../../Navigation"
//        text: qsTr("S C H E R A M A T A  35\ndevelopment")
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int child
    Rectangle {
        id : rootTitle
        width:  root.width
        height:  Style.dimensions.heightZoneElement - 10
        radius: 4
        Layout.alignment: Qt.AlignTop
        RowLayout{
            anchors.fill: parent
            spacing: 1
            Item {
                Layout.fillHeight: true
                Layout.preferredWidth: Style.dimensions.iconSize * 2
                RoburText {
                    text: qsTr("label_2char_ID")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("tipo")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: "c/r"
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    //DOVREBBE ESSERCI LA PAROLA IMPIANTO
                    text: qsTr("title_plant_control")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("abilita")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
    Item{
        id : listModel
        anchors.top:  rootTitle.bottom
        anchors.topMargin: 8
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: button.top
        anchors.bottomMargin: 8
        ListView {
            id: listView
            anchors.topMargin: 8
            anchors.fill: parent
            orientation: ListView.Vertical
            interactive: true
            spacing: 4
            property bool timerToggle: false
            clip : true
            flickableDirection: Flickable.VerticalFlick
            boundsBehavior: Flickable.StopAtBounds
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            model: discoveryPage.exclusionFilterProxy
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight: Style.dimensions.heightZoneElement - 10
                implicitWidth: listView.width
                color: Style.colors.white
                radius: 4
                DiscoveryModel{
                    idId : modelData.id.value
                    internalId : modelData.internalId
                    idType : modelData.type.value
                    idUnitType : modelData.unitType.value
                    idImplant : modelData.implant.value
                    idCheckActive : modelData.enable
                    isCheckable : modelData.isCheckable
                }
            }
        }
    }
    RoburIconButton{
        id : button
        icon: "qrc:/icons/arrow.png"
        visible: discoveryPage.startDiscorvery.value
        isButton: true
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 16
        secondColor: Style.colors.white
        isrotated: true
        onClicked: {
            discoveryPage.stopDiscovery()
        }
    }
}
