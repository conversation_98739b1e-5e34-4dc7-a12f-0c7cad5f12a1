import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
import "../../../../Navigation"
Item {
    id : root
    property string idId
    property string internalId
    property string idType
    property string idUnitType
    property string idImplant
    property var  idCheckActive
    property bool isCheckable
    signal clickOnCheckBox
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 2
            RoburText{
                text : root.idId
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idType)
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                icon:  root.idUnitType
                width: height
                height: Style.preferenceSection.cnButton
                isButton: true
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idImplant
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburCheckbox{
                controlCheck : root.idCheckActive.value
                isTexted: false
                anchors.centerIn: parent
                anchors.fill: parent
                enabled: root.isCheckable
                onClicked: {
                    console.debug("isCheckable : " + isCheckable)
                    console.debug("root.idCheckActive.value : " +root.idCheckActive.value )
                    discoveryPage.onModuleChecked(root.internalId,!root.idCheckActive.value)
                }
            }
        }
    }
}
