import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
import "../../../../Navigation"

//        text: qsTr("S C H E R A M A T A  38\ndevelopment")
Rectangle {
    id: root
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: 4
    Rectangle {
        id: containerSection
        height: Style.dimensions.iconSize * 2
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        radius: Style.dimensions.radiusCorner
        color: Style.colors.lightGray
        opacity: 0.7
        border.color: Style.colors.orange
        RoburText {
            text: qsTr("selezione_riscaldam_acs_base")
            width: parent.width
            height: parent.height
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            font.pixelSize: Style.dimensions.fontBigSize
            color: Style.colors.orange
        }
    }
    RowLayout {
        id: secondoBox
        anchors.top: containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: button.top
        anchors.margins: 16
        AnimatedContainer {
            id: setRiscEACS
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width / 4
            heightToOccupe: secondoBox.height / 4
            isActive: acsBaseSelectionPage.model.acsBaseOnlyHeating.value
            activeColor: Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox {
                id: check2
                labelName: qsTr("solo_riscaldamento")
                anchors.fill: parent
                firstColor: acsBaseSelectionPage.model.acsBaseOnlyHeating.value ? Style.colors.white : Style.colors.darkGray
                secondColor: acsBaseSelectionPage.model.acsBaseOnlyHeating.value ? Style.colors.darkGray : Style.colors.darkGray
                controlCheck: setRiscEACS.isActive
                onClicked: {
                    acsBaseSelectionPage.model.selectOption(false);
                }
            }
        }
        AnimatedContainer {
            id: setRisc
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width / 4
            heightToOccupe: secondoBox.height / 4
            isActive: acsBaseSelectionPage.model.acsBaseHeatingAndAcs.value
            activeColor: Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox {
                id: check3
                labelName: qsTr("riscaldamento_e_acs_contemp")
                anchors.fill: parent
                firstColor: acsBaseSelectionPage.model.acsBaseHeatingAndAcs.value ? Style.colors.white : Style.colors.darkerGray
                secondColor: acsBaseSelectionPage.model.acsBaseHeatingAndAcs.value ? Style.colors.darkGray : Style.colors.darkGray
                controlCheck: setRisc.isActive
                onClicked: {
                    console.debug(check3.controlCheck);
                    acsBaseSelectionPage.model.selectOption(true);
                }
            }
        }
    }
    RoburIconButton {
        id: button
        icon: "qrc:/icons/arrow.png"
        isButton: true
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 16
        isrotated: true
        secondColor: Style.colors.gray
        onClicked: {
            acsBaseSelectionPage.sendBaseAcsSelection();
        }
    }
}
