import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
Item {
    id : root
    property string idId
    property string idType
    property string idUnitType
    property string idImplant
    property string  idCategory
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 2
            RoburText{
                text : root.idId
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text :qsTr( root.idType)
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                icon:  root.idUnitType
                width: height
                height: Style.preferenceSection.cnButton
                isButton: true
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idImplant
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idCategory
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
}
