import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
Rectangle {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    radius : Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("imposta_indirizzo_modbus") + " " + qsTr("controllo_impianto_ta")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("parametri_comunicaz_modbus")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                }
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini {
            label : qsTr("imposta_indirizzo_modbus")
            isAnimated: true
            candidateValue: 0
            Layout.fillHeight: true
            Layout.preferredWidth: parent.width /2
            btnSize : 50
            widthToOccupe: parent.width / 2
            heightToOccupe: parent.height / 2
        }
        AnimatedContainer {
            Layout.fillHeight: true
            Layout.preferredWidth: parent.width /2
            widthToOccupe: firstBox.width / 2
            heightToOccupe: firstBox.height / 2
            isAnimated: true
            activeColor:  Style.colors.orange
            inactiveColor:  Style.colors.lightGray
            RoburCheckbox{
                id : idcheckControlTA
                labelName: qsTr("controllo_impianto_ta")
                anchors.fill: parent
                firstColor:parent.isActive ? Style.colors.white :  Style.colors.darkerGray
                secondColor:parent.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: parent.isActive
                onClicked:
                {
                    parent.isActive = !parent.isActive
                }
            }
        }
    }
    RowLayout{
        id : secondBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer {
            Layout.fillWidth: true
            Layout.fillHeight: true
            RoburText {
                id : idComunicationParameter
                text:  qsTr("parametri_comunicaz_modbus") + " : "
                anchors.top: parent.top
                anchors.horizontalCenter: parent.horizontalCenter
            }
            Item{
                id : container
                anchors.topMargin: 36
                anchors.top: idComunicationParameter.bottom
                anchors.fill: parent
                ListView {
                    id : list
                    anchors.fill: parent
                    spacing: 8
                    model: ModelElementComunicationParameter {}
                    clip : true
                    interactive: true
                    flickableDirection: Flickable.VerticalFlick
                    boundsBehavior: Flickable.StopAtBounds
                    ScrollBar.vertical: ScrollBar {
                        policy: ScrollBar.AlwaysOn
                        active: ScrollBar.AlwaysOn
                    }
                    delegate:Rectangle
                    {
                        required property string version
                        required property string number
                        required property var index
                        width : list.width - 20
                        height : 60
                        radius : Style.dimensions.radiusCorner
                        color : Style.colors.lightGray
                        RoburText{
                            anchors.centerIn: parent
                            text : number + " " + version
                        }
                    }
                }
            }
        }
    }
}
