import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
Item {
    id : root
    property string idId
    property string idType
    property string idUnitType
    property string idImplant
    property var idCategory
    property var availableCategories

    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing: 8
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 2
            RoburText{
                text : root.idId
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text :qsTr( root.idType)
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                icon:  root.idUnitType
                width: height
                height: Style.preferenceSection.cnButton
                isButton: true
                anchors.centerIn: parent
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idImplant
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                labelTextButton: root.idCategory.value
                writable: true
                anchors.centerIn: parent
                height: width
                isButton: true
                width: Style.preferenceSection.cnButton
                onClicked: {
                    var currentIndex = root.availableCategories.indexOf(root.idCategory.value);

                    var nextIndex = (currentIndex + 1) % root.availableCategories.length;

                    var newCategory = root.availableCategories[nextIndex];

                    root.idCategory.setValue(newCategory);

                    console.log("Module ID:", root.idId, "Old Category:", currentIndex, "New Category:", newCategory, "Available:", root.availableCategories);
                }
            }
        }
    }
}
