import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    RowLayout{
        anchors.fill: parent
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            ColumnLayout{
                anchors.fill: parent
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    SectionNavigator{
                        nameSubSection:qsTr("gestione_macchine")
                        icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel);
                        subsectionToGoFirst : SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel
                        subsectionToGoSecond:SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel
                        subsectionToGo: SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel
                        isTwoTypeService : pipesController.isTwoPipe.value
                        onChange: {
                            machineManagementPage.filter.filterString = "SNOW"
                        }
                        onFirstPress:
                        {
                            machineManagementPage.filter.filterString = "SNOW"
                            machineManagementPage.activeFilterProxyID.value = false
                        }
                        onSecondPress:  {
                            machineManagementPage.filter.filterString = "SUN"
                            machineManagementPage.activeFilterProxyID.value = false
                        }
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    SectionNavigator{
                        subsectionToGo: SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
                        nameSubSection: qsTr("working_mode")
                        icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_AGREE.subSectionLabel);
                        subsectionToGoFirst : SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
                        subsectionToGoSecond: SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
                        isTwoTypeService : pipesController.twoPipesVisibleSetter.value
                        onChange: {
                            agreePage.onlyView.value = false
                        }
                        onFirstPress:
                        {
                            agreePage.onlyView.value = false
                            agreePage.filter.filterString = "SNOW"
                        }
                        onSecondPress:  {
                            agreePage.onlyView.value = false
                            agreePage.filter.filterString = "SUN"
                        }
                    }
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            ColumnLayout{
                anchors.fill: parent
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: preferencePage.password.unlockInstallConfiguration.value
                    SectionNavigator{
                        // icon : "qrc:/icons/panel.png"
                        icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_DDCBOARD.subSectionLabel);
                        nameSubSection:  qsTr("pannello_controllo")
                        subsectionToGo: SubSectionEnum.SUBSECTION_DDCBOARD.subSectionLabel
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    visible: !preferencePage.password.unlockInstallConfiguration.value
                    SectionNavigator{
                        // icon : "qrc:/icons/admin-panel.png"
                        icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SETTINGS_INSTALL_CONFIGURATION.subSectionLabel);
                        nameSubSection: qsTr("installazione_e_configurazioni")
                        subsectionToGo: SubSectionEnum.SUBSECTION_SETTINGS_INSTALL_CONFIGURATION.subSectionLabel
                    }
                }
                Item{
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    visible: preferencePage.password.unlockInstallConfiguration.value
                    SectionNavigator{
                        icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_IMPLANTBOARD.subSectionLabel);
                        nameSubSection:  qsTr("impianti")
                        subsectionToGo: SubSectionEnum.SUBSECTION_IMPLANTBOARD.subSectionLabel
                        subsectionToGoFirst : SubSectionEnum.SUBSECTION_IMPLANTBOARD.subSectionLabel
                        subsectionToGoSecond: SubSectionEnum.SUBSECTION_IMPLANTBOARD.subSectionLabel
                        isTwoTypeService : pipesController.isTwoPipe.value
                    }
                }
            }
        }
    }
}
