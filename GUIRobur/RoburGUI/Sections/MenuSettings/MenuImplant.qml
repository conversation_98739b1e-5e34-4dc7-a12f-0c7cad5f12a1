import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    RowLayout{
        anchors.fill: parent
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                // icon : "qrc:/icons/alert.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_CIRCOLAR_EXIT_ALARM.subSectionLabel);
                nameSubSection:  qsTr("modo_circolatore_e_uscita_allarmi")
                subsectionToGo : SubSectionEnum.SUBSECTION_CIRCOLAR_EXIT_ALARM.subSectionLabel
                type:navigator.pendingType
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            visible: (navigator.pendingType === "SNOW" ? true : false)
            SectionNavigator{
                // icon : "qrc:/icons/water-tap.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_IMPLANT_SEPARABLE.subSectionLabel);
                nameSubSection:qsTr("parte_impianto_separabile")
                subsectionToGo :SubSectionEnum.SUBSECTION_IMPLANT_SEPARABLE.subSectionLabel
                type: navigator.pendingType
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                // icon : "qrc:/icons/drop-silhouette.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_IMPLANT_BASE.subSectionLabel);
                nameSubSection: qsTr("parte_impianto_base")
                subsectionToGo: SubSectionEnum.SUBSECTION_IMPLANT_BASE.subSectionLabel
                type: navigator.pendingType
            }
        }
    }
}
