import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
Rectangle {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    radius: 4
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("modo_circolatore_e_uscita_allarmi")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("config_uscita_allarmi")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                }
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            id : idWaterPump
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: firstBox.width/ 4
            heightToOccupe: firstBox.height/ 4
            isAnimated: !check4.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check4
                labelName: qsTr("water_pump_mode_common_pump")
                anchors.fill: parent
                firstColor:idWaterPump.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idWaterPump.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idWaterPump.isActive
            }
            onDisableAll: {
                idWaterPump.isActive = true
                idIndipendente.isActive = false
            }
        }
        AnimatedContainer{
            id : idIndipendente
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: firstBox.width/ 4
            heightToOccupe: firstBox.height/ 4
            isAnimated: !check5.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check5
                labelName: qsTr("water_pump_mode_units_pump")
                anchors.fill: parent
                firstColor:idIndipendente.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idIndipendente.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idIndipendente.isActive
            }
            onDisableAll: {
                idWaterPump.isActive = false
                idIndipendente.isActive = true
            }
        }
    }
    RowLayout{
        id : secondoBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            id : idDisabilita
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width / 4
            heightToOccupe: secondoBox.height/ 4
            isAnimated: !check1.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check1
                labelName: qsTr("disabilita")
                anchors.fill: parent
                firstColor:idDisabilita.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idDisabilita.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idDisabilita.isActive
            }
            onDisableAll: {
                idDisabilita.isActive = true
                idAnyAlarm.isActive = false
                idAlarmTWater.isActive = false
            }
        }
        AnimatedContainer{
            id : idAnyAlarm
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width/ 4
            heightToOccupe: secondoBox.height/ 4
            isAnimated: !check2.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check2
                labelName: qsTr("qualsiasi_allarme")
                anchors.fill: parent
                firstColor:idAnyAlarm.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idAnyAlarm.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idAnyAlarm.isActive
            }
            onDisableAll: {
                idDisabilita.isActive = false
                idAnyAlarm.isActive = true
                idAlarmTWater.isActive = false
            }
        }
        AnimatedContainer{
            id : idAlarmTWater
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width/ 4
            heightToOccupe: secondoBox.height/ 4
            isAnimated: !check3.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check3
                labelName: qsTr("allarme_temp_acqua")
                anchors.fill: parent
                firstColor:idAlarmTWater.isActive ? Style.colors.white :  Style.colors.darkerGray
                secondColor:idAlarmTWater.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idAlarmTWater.isActive
            }
            onDisableAll: {
                idDisabilita.isActive = false
                idAnyAlarm.isActive = false
                idAlarmTWater.isActive = true
            }
        }
    }
}
