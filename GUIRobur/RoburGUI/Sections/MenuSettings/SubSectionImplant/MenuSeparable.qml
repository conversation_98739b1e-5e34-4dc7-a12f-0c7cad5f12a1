import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item{
    id: frame
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int heightElements: 75
    property int distance: 4
    Flickable {
        id : flickable
        anchors.fill: parent
        contentWidth: frame.width
        contentHeight:  pipesController.isTwoPipe.value ?
        col.children.length * heightElements + distance * (col.children.length)
        :
        (col.children.length - 1 ) * heightElements + distance * (col.children.length - 1)
        clip: true
        boundsBehavior: Flickable.StopAtBounds
        ScrollIndicator.vertical: ScrollIndicator {
            parent: flickable.parent
            anchors.top: flickable.top
            anchors.bottom: flickable.bottom
            anchors.left: flickable.right
            width: 11
            active: true
            visible: true  // Show it always
            opacity: 1.0   // Fully visible
            // Prevent Flickable from deactivating it
            onActiveChanged: {
                if (!active) active = true;
            }
        }
        Column{
            id : col
            anchors.fill: parent
            spacing: 4
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_ACSSETMAXWAT.subSectionLabel);
                    nameSubSection: qsTr("selezoione_riscaldam_acs") + " " + qsTr("setpoint_acqua_max_acs_separab")
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_ACSSETMAXWAT.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_POWIMPLANTINIT.subSectionLabel);
                    nameSubSection: qsTr("differenziale_acs_separabile") + " " + qsTr("potenza_iniziale_acs_separabile")
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_POWIMPLANTINIT.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGOLATION.subSectionLabel);
                    nameSubSection: qsTr("potenza_nominale_impianto") + " " + qsTr("regulation_param")
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGOLATION.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_SEPARABLESER_RBOXVFASEVALVE.subSectionLabel);
                    nameSubSection: qsTr("ritardo_circolatore_rbox")
                    + " " + qsTr("temporizzaz_fase_commutazione")
                    + " " + qsTr("parametri_valvola_separazione")
                    subsectionToGo: SubSectionEnum.SUBSECTION_SEPARABLESER_RBOXVFASEVALVE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_GAPH_REGOLATION_MODE.subSectionLabel);
                    nameSubSection: "priorita_w"
                    subsectionToGo: SubSectionEnum.SUBSECTION_GAPH_REGOLATION_MODE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                visible: pipesController.isTwoPipe.value
                SectionNavigator{
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_VALVEPARAM.subSectionLabel);
                    nameSubSection: "parametri_valvola_inversione"
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_VALVEPARAM.subSectionLabel
                    type:  navigator.pendingType
                }
            }
        }
    }
}
