import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../.."
import "../../../Components"
import "../../../Testing"
import "../../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int heightElements: 75
    property int distance: 4
    id: frame
    Flickable {
        id : flickable
        anchors.fill: parent
        contentWidth: frame.width
        contentHeight:  navigator.pendingType === "SNOW" ?
        col.children.length * heightElements + distance * (col.children.length - 1)
        :
        (col.children.length - 3 ) * heightElements + distance * (col.children.length - 3)
        clip: true
        boundsBehavior: Flickable.StopAtBounds
        ScrollIndicator.vertical: ScrollIndicator {
            parent: flickable.parent
            anchors.top: flickable.top
            anchors.bottom: flickable.bottom
            anchors.left: flickable.right
            width: 11
            active: true
            visible: true  // Show it always
            opacity: 1.0   // Fully visible
            // Prevent Flickable from deactivating it
            onActiveChanged: {
                if (!active) active = true;
            }
        }
        Column{
            id : col
            anchors.fill: parent
            spacing: 4
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    // === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_WATERBOLERPOWER.subSectionLabel);
                    nameSubSection: qsTr("differenziale_acqua") + " " +
                    qsTr("potenza_iniziale") + " " +
                    qsTr("funzione_caldaie_ausiliarie") +  " " +
                    qsTr("potenza_nominale_impianto")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_WATERBOLERPOWER.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_PARAMREGOLATION.subSectionLabel);
                    nameSubSection: qsTr("regulation_param")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_PARAMREGOLATION.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_RBOXCIRCOLARRIT.subSectionLabel);
                    nameSubSection:  qsTr("ritardo_circolatore_rbox")
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_RBOXCIRCOLARRIT.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_PREUSEMACHINE.subSectionLabel);
                    nameSubSection: qsTr("precedenza_uso_macchine")
                    subsectionToGo: SubSectionEnum.SUBSECTION_HEAT_BASEIMPLANT_PREUSEMACHINE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_ERRCONGTEMP.subSectionLabel);
                    nameSubSection: qsTr("config_errori_temp")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_ERRCONGTEMP.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                //qua cambia riscaldamento oppure condizionamento
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_CONFIGURATION_SERVICE.subSectionLabel);
                    nameSubSection:  navigator.pendingType === "SUN" ?   qsTr("config_serv_condizionam") : qsTr("config_serv_riscald")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_CONFIGURATION_SERVICE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                visible: navigator.pendingType === "SNOW" ? true : false
                //qua cambia riscaldamento oppure condizionamento
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_SELSERVACSS.subSectionLabel);
                    nameSubSection: qsTr("selezoione_riscaldam_acs")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_SELSERVACSS.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                visible: navigator.pendingType === "SNOW" ? true : false
                //qua cambia riscaldamento oppure condizionamento
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_BASEIMPLANT_SELSERVACSS.subSectionLabel);
                    nameSubSection: qsTr("config_acs_base")
                    subsectionToGo: SubSectionEnum.SUBSECTION_BASEIMPLANT_CONFIGURATION_ACS_BASE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
            Item{
                height: heightElements
                width: parent.width
                visible: navigator.pendingType === "SNOW" ? true : false
                SectionNavigator{
                    // icon : " navigator.pendingType === "SUN" ? "qrc:/icons/sun.png" : "qrc:/icons/snowflake.png"
                    icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_GAPH_REGOLATION_MODE.subSectionLabel);
                    nameSubSection:"Modalità regolazione GAHP"// qsTr("")
                    subsectionToGo: SubSectionEnum.SUBSECTION_GAPH_REGOLATION_MODE.subSectionLabel
                    type:  navigator.pendingType
                }
            }
        }
    }
}
