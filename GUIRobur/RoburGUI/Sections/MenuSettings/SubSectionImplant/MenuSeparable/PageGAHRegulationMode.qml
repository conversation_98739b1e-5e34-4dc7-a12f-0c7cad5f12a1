import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    property bool activeSection1Label : true
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("priorita_w")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
    }
    RowLayout{
        id : secondoBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            id : setRiscEACS
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width/ 4
            heightToOccupe: secondoBox.height/ 4
            isAnimated: true
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check2
                labelName: qsTr("modulanti")
                anchors.fill: parent
                firstColor:setRiscEACS.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:setRiscEACS.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck:setRiscEACS.isActive
                onClicked: {
                    setRiscEACS.isActive = true
                    setRisc.isActive = false
                }
            }
        }
        AnimatedContainer{
            id : setRisc
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width/ 4
            heightToOccupe: secondoBox.height/ 4
            isAnimated: true
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check3
                labelName: qsTr("on_off")
                anchors.fill: parent
                firstColor:setRisc.isActive ? Style.colors.white :  Style.colors.darkerGray
                secondColor:setRisc.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck:setRisc.isActive
                onClicked:
                {
                    setRiscEACS.isActive = false
                    setRisc.isActive = true
                }
            }
        }
    }
}
