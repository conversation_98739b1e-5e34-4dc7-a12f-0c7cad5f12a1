import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    radius: Style.dimensions.radiusCorner
    Layout.fillWidth: true
    Layout.fillHeight: true
    Item{
        width: parent.width
        height: 100
        anchors.bottom: container.top
        RoburText {
            text: qsTr("effettuare_config_semiauto")
            width: parent.width
            height: parent.height
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    Item{
        id : container
        anchors.verticalCenter: parent.verticalCenter
        anchors.left: parent.left
        anchors.right: parent.right
        height:  100
        RowLayout{
            anchors.fill: parent
            anchors.leftMargin: 32
            anchors.rightMargin: 32
            AnimatedConfirm
            {
                label :  qsTr("yes")
                Layout.fillWidth: true
                Layout.fillHeight: true
                widthToOccupe: container.width
                heightToOccupe: container.height
                isAnimated: true
                onGoTo:  {
                    navigator.navigate(SubSectionEnum.SUBSECTION_HEAT_SEPARABLESER_PARAMREGROBUR.subSectionLabel)
                }
            }
            AnimatedConfirm
            {
                label :  qsTr("no")
                Layout.fillWidth: true
                Layout.fillHeight: true
                widthToOccupe: container.width
                heightToOccupe: container.height
                isAnimated: true
            }
        }
    }
}
