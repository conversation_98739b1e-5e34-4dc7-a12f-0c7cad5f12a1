import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    radius: 4
    Layout.fillWidth: true
    Layout.fillHeight: true
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("ritardo_circolatore_rbox") +  " " + qsTr("parametri_valvola_separazione")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("Tempo OFF macchina") + " " + qsTr("ritardo_inserim_media_imp_separarato")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                }
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini{
            label : qsTr("ritardo_circolatore_rbox")
            unit: qsTr("s")
            btnSize :  50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
        RoburCounterMini{
            label : qsTr("parametri_valvola_separazione")
            unit: "s"
            btnSize :  50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
    }
    RoburText{
        id : idText
        text: qsTr("temporizzaz_fase_commutazione") + " : "
        anchors.top:containerSection.bottom
        anchors.topMargin: 8
        anchors.right: parent.right
        anchors.left: parent.left
        anchors.leftMargin:   16
        height: Style.dimensions.iconSize * 2
        visible: root.activeSection2Label
    }
    RowLayout{
        id : container
        visible: root.activeSection2Label
        anchors.top:idText.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        height: 200
        anchors.margins: 16
        RoburCounterMini{
            id : thirdBox
            label: qsTr("Tempo OFF macchina")
            unit:  qsTr("s")
            btnSize :  50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: container.width
            heightToOccupe: container.height
        }
        RoburCounterMini{
            id : fouthBox
            btnSize :  50
            label : qsTr("ritardo_inserim_media_imp_separarato")
            unit:  qsTr("s")
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: container.width
            heightToOccupe: container.height
        }
    }
}
