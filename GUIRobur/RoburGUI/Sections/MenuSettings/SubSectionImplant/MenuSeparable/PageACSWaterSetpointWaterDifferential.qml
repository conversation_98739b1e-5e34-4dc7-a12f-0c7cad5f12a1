import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../../../Testing"

Rectangle {
    id: root
    property bool isTab1Active: acsSeparableSelectionPage.model.tab1Visible.value
    property bool activeSection1Label: true
    property bool activeSection2Label: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: Style.dimensions.radiusCorner
    RowLayout {
        id: containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            visible: acsSeparableSelectionPage.model.tab1Visible.value
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius: Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("riscaldamento_e_acs_non_contemp")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    root.activeSection1Label = true;
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            visible: acsSeparableSelectionPage.model.tab2Visible.value
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius: Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("setpoint_acqua_max_acs_separab") + " " + qsTr("differenziale_acs_separabile")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    root.activeSection1Label = false;
                    root.activeSection2Label = true;
                }
            }
        }
    }
    ConfigurationMachineACSSeparabile {
        id: firstBox
        visible: root.isTab1Active && acsSeparableSelectionPage.model.tab1Visible.value
        Layout.fillWidth: true
        Layout.fillHeight: true
        model: acsSeparableSelectionPage.model
    }
    RowLayout {
        id: secondoBox
        visible: root.activeSection2Label
        anchors.top: containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini {
            label: qsTr("setpoint_acqua_max_acs_separab")
            btnSize: 50
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width / 2
            heightToOccupe: secondoBox.height / 2
        }
        RoburCounterMini {
            label: qsTr("differenziale_acs_separabile")
            btnSize: 50
            isAnimated: true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondoBox.width / 2
            heightToOccupe: secondoBox.height / 2
        }
    }
}
