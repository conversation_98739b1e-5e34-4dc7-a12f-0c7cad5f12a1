import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    property bool selection1: true
    property bool selection2: false
    property bool selection3: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: 4
    RowLayout{
        id: idPlant
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.cnButton
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RowLayout{
                anchors.fill: parent
                Item{
                    Layout.fillHeight: true
                    Layout.preferredWidth:   Style.dimensions.cnButton
                    RoburIconButton{
                        icon: "qrc:/icons/arrow.png"
                        isButton: true
                        height:  Style.dimensions.cnButton
                        width: height
                        anchors.centerIn: parent
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburDataViewer {
                        labelName: qsTr("id - caldaia robur AY120")
                        anchors.centerIn: parent
                        anchors.fill: parent
                        anchors.leftMargin: 32
                        anchors.rightMargin:  32
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.preferredWidth:   Style.dimensions.cnButton
                    RoburIconButton{
                        icon: "qrc:/icons/arrow.png"
                        isrotated: true
                        isButton: true
                        height: Style.dimensions.cnButton
                        width: height
                        anchors.centerIn: parent
                    }
                }
                Item{
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                }
            }
        }
    }
    RowLayout{
        id : selector
        anchors.top: idPlant.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection1 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection1 ? 0.7 : 0.4
            border.color:root.selection1 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :  qsTr("potenza_termica") + "\n" + qsTr("Priorità") + " " + qsTr("accensione")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.selection1 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = true
                    root.selection2 = false;
                    root.selection3 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :  qsTr("min_run_time") + "\n" + qsTr("locking_time")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                    root.selection3 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection3 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection3 ? 0.7 : 0.4
            border.color:root.selection3 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :  qsTr("numero_stadi") + "\n" + qsTr("ritardo_circolatore")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection3 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = false;
                    root.selection3 = true;
                }
            }
        }
    }
    RowLayout
    {
        id : firstBox
        visible: root.selection1
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("potenza_termica")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
        RoburCounterMini{
            label : qsTr("Priorità") + " " + qsTr("accensione")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
    }
    RowLayout
    {
        id : secondBox
        visible: root.selection2
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("locking_time")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
        }
        RoburCounterMini{
            label : qsTr("min_run_time")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
        }
    }
    RowLayout
    {
        id : thirdBox
        visible: root.selection3
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("numero_stadi")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: thirdBox.width
            heightToOccupe: thirdBox.heightht
        }
        RoburCounterMini{
            label : qsTr("ritardo_circolatore")
            btnSize : 40
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: thirdBox.width
            heightToOccupe: thirdBox.height
        }
    }
}
