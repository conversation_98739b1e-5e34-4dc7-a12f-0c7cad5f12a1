import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    property bool activeSection1Label : true
    property bool activeSection2Label: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("potenza_iniziale_acs_separabile")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("potenza_nominale_acs_separabile")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = false
                    root.activeSection2Label = true;
                }
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        RoburCounterMini{
            label : qsTr("init_power_percentage")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            btnSize: 50
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
            onDownClicked: {
            }
            onUpClicked: {
            }
        }
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
    RowLayout{
        id : secondBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight:  true
            widthToOccupe: secondBox.width /2
            heightToOccupe: secondBox.height / 2
            isAnimated: true
            activeColor : Style.colors.no
            inactiveColor: Style.colors.no
            ColumnLayout
            {
                spacing: 8
                anchors.fill: parent
                AnimatedContainer{
                    id : idDefault
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    widthToOccupe: parent.width
                    heightToOccupe: parent.height
                    isAnimated: !idDefault.isActive
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.lightGray
                    RoburCheckbox{
                        labelName : qsTr("default")
                        anchors.fill: parent
                        firstColor:idDefault.isActive ? Style.colors.white :  Style.colors.darkGray
                        secondColor:idDefault.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                        controlCheck: true
                        onClicked: {
                            idDefault.isActive = true
                            idCustom.isActive = false
                        }
                    }
                }
                AnimatedContainer{
                    id : idCustom
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    widthToOccupe: parent.width
                    heightToOccupe: parent.height
                    isAnimated:!idCustom.isActive
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.lightGray
                    RoburCheckbox{
                        labelName: qsTr("custom")
                        anchors.fill: parent
                        firstColor:idCustom.isActive ? Style.colors.white :  Style.colors.darkGray
                        secondColor:idCustom.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                        controlCheck: true
                        onClicked:  {
                            idDefault.isActive = false
                            idCustom.isActive = true
                        }
                    }
                }
            }
        }
        RoburCounterMini{
            label :  qsTr("custom")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
            visible: idCustom.isActive
            Layout.fillHeight: true
            Layout.preferredWidth: parent.width/2
            isAnimated: true
            btnSize: 50
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
            onDownClicked: {
            }
            onUpClicked: {
            }
        }
    }
}
