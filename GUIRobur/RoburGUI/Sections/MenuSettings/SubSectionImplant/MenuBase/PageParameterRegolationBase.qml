import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../../../.."
import "../../../../Components"
import "../../SettingsComponents"
Item {
    id: root
    Layout.fillWidth: true
    Layout.fillHeight: true
    property int leftMarginAnchor: 16
    property int rightMarginAnchor: 16
    Component {
        id: delegate
        Item{
            id: wrapper
            anchors.fill: parent
            visible: PathView.isCurrentItem ? true : false
            required property var modelData
            required property var index
            ModelParRegBase{
                idID : modelData.id
                thermalPower:  modelData.thermalPower
                ignitionPriority:  modelData.ignitionPriority
                inhibitionTime:  modelData.inhibitionTime
                miniumInhibitionTime : modelData.miniumInhibitionTime
                numberOfStages : modelData.numberOfStages
                coolingCapacity : modelData.coolingCapacity
                circulatorDelay : modelData.circulatorDelay
                isThirdPart : modelData.isThirdPartMachine
                onGoToLefthPathIndex: {
                    pathview.decrementCurrentIndex()
                }
                onGoToRigthPathIndex: {
                    pathview.incrementCurrentIndex()
                }
            }
        }
    }
    PathView {
        id : pathview
        anchors.fill: parent
        model: parameterRegolationBasePage.model
        interactive : false
        delegate: delegate
        path: Path {
            id: path
            startX:root.width/2; startY: root.height/2
            PathQuad {
                x: root.width;
                y: root.height/2;
                controlX: root.width;
                controlY: root.height/2
            }
            PathQuad {
                x: 0;
                y: root.height/2;
                controlX: 0;
                controlY: root.height/2}
            }
        }
    }
