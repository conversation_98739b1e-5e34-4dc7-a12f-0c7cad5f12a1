import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../../.."
import "../../../../../Components"
import "../../../../../Testing"
Rectangle {
    id : root
    property bool activeSection1Label : true
    property bool activeSection2Label : false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: 4
    RowLayout{
        id : titleContainer
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: navigator.pendingType === "SUN" ? qsTr("limite_potenza_max_in_condiz") :  qsTr("limite_potenza_max_in_riscald")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
    }
    RowLayout{
        id : containerSection
        anchors.top: titleContainer.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        AnimatedContainer{
            id : checkBoxContainer1
            visible: navigator.pendingType === "SUN" ? false : true
            Layout.fillHeight: true
            Layout.fillWidth: true
            isActive: true
            widthToOccupe: containerSection.width/ 4
            heightToOccupe: containerSection.height/ 4
            isAnimated: !check1.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check1
                labelName: qsTr("limite_potenza_max_su_T_esterna")
                anchors.fill: parent
                firstColor:checkBoxContainer1.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:checkBoxContainer1.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: true
            }
            onDisableAll: {
                checkBoxContainer1.isActive = true
                checkBoxContainer2.isActive = false
                checkBoxContainer3.isActive = false
            }
        }
        AnimatedContainer{
            id : checkBoxContainer2
            visible: navigator.pendingType === "SUN" ? false : true
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: containerSection.width/ 4
            heightToOccupe: containerSection.height/ 4
            isAnimated: !check2.controlCheck
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                id : check2
                labelName: qsTr("inibisci_caldaie")
                anchors.fill: parent
                firstColor:checkBoxContainer2.isActive ? Style.colors.white :  Style.colors.darkerGray
                secondColor:checkBoxContainer2.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: checkBoxContainer2.isActive
            }
            onDisableAll: {
                checkBoxContainer1.isActive = false
                checkBoxContainer2.isActive = true
            }
        }
        AnimatedContainer{
            id : checkBoxContainer3
            visible: navigator.pendingType === "SUN" ? true : false
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: containerSection.width/ 4
            heightToOccupe: containerSection.height/ 4
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            isAnimated: true
            isActive: check3.isChecked
            Item{
                id : text
                anchors.left: parent.left
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                anchors.right: check3.left
                anchors.rightMargin: 16
                RoburText
                {
                    text: qsTr("limite_potenza_max_su_T_esterna")
                    anchors.fill: parent
                    horizontalAlignment :  Text.AlignHCenter
                    color: check3.isChecked ? Style.colors.white : Style.colors.darkGray
                }
            }
            RoburSwitch
            {
                id : check3
                anchors.verticalCenter: text.verticalCenter
                anchors.right: parent.right
                anchors.rightMargin: 16
                width: 80
                height: 40
                activeBaseColor: Style.colors.white
                inactiveBaseColor: Style.colors.darkGray
                pivotDisabledBorderColor: Style.colors.darkGray
                pivotUpBorderColor: Style.colors.white
                pivotUpColor: Style.colors.darkOrange
                isChecked: true
                onClicked:
                {
                    checkBoxContainer3.isActive = check3.isChecked
                    checkBoxContainer1.isActive = checkBoxContainer3.isActive
                }
            }
            onClicked:
            {
                check3.isChecked = checkBoxContainer3.isActive
                checkBoxContainer1.isActive =  check3.isChecked
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: checkBoxContainer1.isActive
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        RoburCounterMini{
            label : qsTr("perc_100_con_temp_esterna_di")
            btnSize : 50
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
        RoburCounterMini{
            label : qsTr("0_perc_con_temp_esterna_di")
            btnSize : 50
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
        RoburCounterMini{
            label : qsTr("ritardo di attivazione")
            btnSize : 50
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
        }
    }
    RowLayout{
        id : secondBox
        visible: checkBoxContainer2.isActive
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
        }
        RoburCounterMini{
            label : qsTr("Temperat_est_inibizione")
            btnSize : 50
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
        }
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight: true
            isAnimated: true
            widthToOccupe: secondBox.width
            heightToOccupe: secondBox.height
        }
    }
}
