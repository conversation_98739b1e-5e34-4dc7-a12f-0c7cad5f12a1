﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../../.."
import "../../../../../Components"
import "../../../../../Testing"
Rectangle {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius : Style.dimensions.radiusCorner
    property bool selection1: true
    property bool selection2: false
    RowLayout{
        id : selector
        anchors.top: parent.top
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection1 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection1 ? 0.7 : 0.4
            border.color:root.selection1 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text : navigator.pendingType === "SUN" ? qsTr("setpoint") :    qsTr("pendenza_curva_climatica") +"\n" + qsTr("influenza_temp_interna")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.selection1 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = true
                    root.selection2 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text : navigator.pendingType === "SUN" ?  qsTr("offset")+ "\n" +  qsTr("influenza_temp_interna")+ "\n" + qsTr("costante_tempo_edificio")  : qsTr("offset") +"\n" +  qsTr("influenza_temp_interna")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                }
            }
        }
    }
    RowLayout
    {
        id : containerFirst
        visible: root.selection1
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label  : navigator.pendingType === "SUN" ? qsTr("setpoint_acqua_T_ext_25_77") : qsTr("pendenza_curva_climatica")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
        }
        RoburCounterMini{
            label :  navigator.pendingType === "SUN" ?  qsTr("setpoint_acqua_T_ext_35_95") :qsTr("influenza_temp_interna")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
        }
    }
    RowLayout
    {
        id : containerSecond
        visible: root.selection2
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            id : first
            label : qsTr("offset")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDisableAll:
            {
                first.isActive = true
                third.isActive = false
                second.isActive = false
            }
        }
        RoburCounterMini{
            id : third
            visible: navigator.pendingType === "SUN"  ? true : false
            label : qsTr("influenza_temp_interna")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDisableAll:
            {
                first.isActive = false
                third.isActive = false
                second.isActive = true
            }
        }
        RoburCounterMini{
            id : second
            label :qsTr("costante_tempo_edificio")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onDisableAll:
            {
                first.isActive = false
                third.isActive = false
                second.isActive = true
            }
        }
    }
}
