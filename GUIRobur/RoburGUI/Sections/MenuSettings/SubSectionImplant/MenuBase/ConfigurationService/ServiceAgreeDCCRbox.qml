import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../../.."
import "../../../../../Components"
import "../../../../../Testing"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    Test{
        RoburText {
            text: qsTr("SCHERMATA 68/ndevelopment")
            anchors.centerIn: parent
            font.bold: true
            Component.onCompleted: {
                navigator.navigate(SubSectionEnum.SUBSECTION_AGREE.subSectionLabel);
            }
        }
    }
}
