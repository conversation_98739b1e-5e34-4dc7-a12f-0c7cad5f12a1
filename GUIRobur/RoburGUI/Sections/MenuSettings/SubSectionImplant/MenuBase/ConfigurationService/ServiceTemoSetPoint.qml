import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../../.."
import "../../../../../Components"
import "../../../../../Testing"
Rectangle {
    id : root
    property bool selection1: true
    property bool selection2: false
    property bool selection3: false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius : Style.dimensions.radiusCorner
    RowLayout{
        id : selector
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection1 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection1 ? 0.7 : 0.4
            border.color:root.selection1 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :qsTr("thermostatation_mode")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.selection1 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = true
                    root.selection2 = false;
                    root.selection3 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :qsTr("setpoint_acqua_max")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                    root.selection3 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection3 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection3 ? 0.7 : 0.4
            border.color:root.selection3 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text :qsTr("setpoint_acqua_min")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection3 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = false;
                    root.selection3 = true;
                }
            }
        }
    }
    ColumnLayout
    {
        id : containerFirst
        visible: root.selection1
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        AnimatedContainer{
            id : one
            Layout.fillWidth: true
            Layout.fillHeight:true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            isAnimated: false
            activeColor: Style.colors.orange
            inactiveColor: Style.colors.lightGray
            isActive: idSullaMandata.controlCheck
            RoburCheckbox{
                id : idSullaMandata
                controlCheck: setpointMinMaxTermostationPage.thermostatationGS.currentValue  ===setpointMinMaxTermostationPage.thermostatationGS.getItem(0)
                labelName: qsTr(setpointMinMaxTermostationPage.thermostatationGS.getDisplayItem(0))
                width: parent.width / 2
                anchors.centerIn: parent
                firstColor:controlCheck ? Style.colors.white :  Style.colors.darkGray
                secondColor:controlCheck ? Style.colors.darkGray :  Style.colors.gray
            }
            MouseArea{
                anchors.fill: parent
                onClicked: {
                    setpointMinMaxTermostationPage.thermostatationGS.currentValue  = setpointMinMaxTermostationPage.thermostatationGS.getItem(0)
                }
            }
        }
        AnimatedContainer{
            id : two
            Layout.fillWidth: true
            Layout.fillHeight:true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            isAnimated: false
            activeColor:Style.colors.orange
            inactiveColor: Style.colors.lightGray
            isActive: idSulRitorno.controlCheck
            RoburCheckbox{
                id : idSulRitorno
                controlCheck: setpointMinMaxTermostationPage.thermostatationGS.currentValue  === setpointMinMaxTermostationPage.thermostatationGS.getItem(1)
                labelName: qsTr(setpointMinMaxTermostationPage.thermostatationGS.getDisplayItem(1))
                width: parent.width / 2
                anchors.centerIn: parent
                firstColor:controlCheck ? Style.colors.white :  Style.colors.darkGray
                secondColor:controlCheck ? Style.colors.darkGray :  Style.colors.gray
            }
            MouseArea{
                anchors.fill: parent
                onClicked: {
                    setpointMinMaxTermostationPage.thermostatationGS.currentValue  = setpointMinMaxTermostationPage.thermostatationGS.getItem(1)
                }
            }
        }
    }
    RowLayout
    {
        id : containerSecond
        visible: root.selection2
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("weather_comp_max_out_setp")
            candidateValue: setpointMinMaxTermostationPage.maxOutGS.value
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onUpClicked: {
                var temp = parseFloat(setpointMinMaxTermostationPage.maxOutGS.value) + 0.1
                setpointMinMaxTermostationPage.maxOutGS.value = temp
            }
            onDownClicked:
            {
                var temp = parseFloat(setpointMinMaxTermostationPage.maxOutGS.value) - 0.1
                setpointMinMaxTermostationPage.maxOutGS.value = temp
            }
        }
        RoburCounterMini{
            label : qsTr("weather_comp_max_in_setp")
            candidateValue: setpointMinMaxTermostationPage.maxInGS.value
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerFirst.width
            heightToOccupe: containerFirst.height
            onUpClicked: {
                var temp = parseFloat(setpointMinMaxTermostationPage.maxInGS.value)+ 0.1
                setpointMinMaxTermostationPage.maxInGS.value = temp
            }
            onDownClicked:
            {
                var temp = parseFloat(setpointMinMaxTermostationPage.maxInGS.value) - 0.1
                setpointMinMaxTermostationPage.maxInGS.value = temp
            }
        }
    }
    RowLayout
    {
        id : containerThird
        visible: root.selection3
        anchors.top: selector.bottom
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.leftMargin: 16
        anchors.right: parent.right
        anchors.rightMargin: 16
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        RoburCounterMini{
            label : qsTr("weather_comp_min_out_setp")
            candidateValue: setpointMinMaxTermostationPage.minOutGS.value
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onUpClicked: {
                var temp = parseFloat(setpointMinMaxTermostationPage.minOutGS.value) + 0.1
                setpointMinMaxTermostationPage.minOutGS.value = temp
            }
            onDownClicked:
            {
                var temp = parseFloat(setpointMinMaxTermostationPage.minOutGS.value) - 0.1
                setpointMinMaxTermostationPage.minOutGS.value = temp
            }
        }
        RoburCounterMini{
            label : qsTr("weather_comp_min_in_setp")
            candidateValue: setpointMinMaxTermostationPage.minInGS.value
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: containerSecond.width
            heightToOccupe: containerSecond.height
            onUpClicked: {
                var temp = parseFloat(setpointMinMaxTermostationPage.minInGS.value) + 0.1
                setpointMinMaxTermostationPage.minInGS.value = temp
            }
            onDownClicked:
            {
                var temp = parseFloat(setpointMinMaxTermostationPage.minInGS.value) - 0.1
                setpointMinMaxTermostationPage.minInGS.value = temp
            }
        }
    }
}
