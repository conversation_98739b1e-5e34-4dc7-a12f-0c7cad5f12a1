import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
import "../../../../Navigation"
Item {
    Layout.fillWidth: true
    Layout.fillHeight: true
    GridLayout{
        anchors.fill: parent
        rows : 2
        columns : 2
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                // icon : "qrc:/icons/sun.png"
                icon : navigator.getIconMenu(SubSectionEnum.SUBSECTION_AGREE.subSectionLabel);
                nameSubSection:  qsTr("config_consensi_ddc_rbox")
                subsectionToGo : SubSectionEnum.SUBSECTION_AGREE.subSectionLabel
                type:  navigator.pendingType
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_TERMOSETPOINT.subSectionLabel);
                nameSubSection:  qsTr("thermostatation_mode") + "\n" +  qsTr("Setpoint_max_min")
                subsectionToGo : SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_TERMOSETPOINT.subSectionLabel
                type:  navigator.pendingType
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_PARAMCURVACLIMATICA.subSectionLabel);
                nameSubSection:  qsTr("parametri_curva_climatica")
                subsectionToGo : SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_PARAMCURVACLIMATICA.subSectionLabel
                type:  navigator.pendingType
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            SectionNavigator{
                icon : navigator.getIconMenu(SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_LIMPOWERMAXHEAT.subSectionLabel);
                nameSubSection: navigator.pendingType === "SUN" ? qsTr("limite_potenza_max_in_riscald")  : qsTr("limite_potenza_max_in_condiz")
                subsectionToGo : SubSectionEnum.SUBSUBSECTION_BASEIMPLANT_CONFIGSERVICE_LIMPOWERMAXHEAT.subSectionLabel
                type:  navigator.pendingType
            }
        }
    }
}
