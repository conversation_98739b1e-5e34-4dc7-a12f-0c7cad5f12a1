import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
Rectangle {
    id : root
    property bool activeSection1Label : true
    property bool activeSection2Label : false
    property bool activeSection3Label : false
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: 4
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("massimo_setpoint_acs")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection2Label = false
                    root.activeSection1Label = true
                    root.activeSection3Label = false
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection2Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection2Label ? 0.7 : 0.4
            border.color:root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("priorita") + " " + qsTr("setpoint")+ " " + qsTr("cald")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection2Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection2Label = true
                    root.activeSection1Label = false
                    root.activeSection3Label = false
                }
            }
        }
    }
    RowLayout{
        id : firstBox
        visible: root.activeSection1Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
        RoburCounterMini{
            label : qsTr("massimo_setpoint_acs")
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            btnSize: 50
            widthToOccupe: firstBox.width
            heightToOccupe: firstBox.height
            onDownClicked: {
            }
            onUpClicked: {
            }
        }
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
        }
    }
    RowLayout{
        id : secondBox
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            id : idDefault
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondBox.width/ 4
            heightToOccupe: secondBox.height/ 4
            isAnimated: !idDefault.isActive
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                labelName : qsTr("massimo_setpoint_acs")
                anchors.fill: parent
                firstColor:idDefault.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idDefault.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idDefault.isActive
                onClicked:  {
                    idDefault.isActive = true
                    idCustom.isActive = false
                }
            }
        }
        AnimatedContainer{
            id : idCustom
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: secondBox.width / 4
            heightToOccupe: secondBox.height/ 4
            isAnimated:!idCustom.isActive
            activeColor : Style.colors.orange
            inactiveColor: Style.colors.lightGray
            RoburCheckbox{
                labelName: qsTr("massimo_setpoint_risc_acs")
                anchors.fill: parent
                firstColor:idCustom.isActive ? Style.colors.white :  Style.colors.darkGray
                secondColor:idCustom.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                controlCheck: idCustom.isActive
                onClicked:  {
                    idDefault.isActive = false
                    idCustom.isActive = true
                }
            }
        }
    }
}
