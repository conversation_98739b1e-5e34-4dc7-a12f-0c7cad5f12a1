import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    property bool activeSection1Label : true
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("ritardo_circolatore_rbox")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                    root.activeSection2Label = false;
                }
            }
        }
    }
    RowLayout{
        id : box
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: containerSection.left
        anchors.right: containerSection.right
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 16
        anchors.topMargin: 16
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight: true
            widthToOccupe: row.width
            heightToOccupe: row.height
            isAnimated: true
        }
        RoburCounterMini{
            id : idRit
            label:  qsTr("ritardo_circolatore_rbox")
            Layout.fillHeight: true
            Layout.fillWidth: true
            btnSize: 50
            widthToOccupe: box.width
            heightToOccupe: box.height
            isAnimated: true
            // activeColor : Style.colors.orange
            // inactiveColor: Style.colors.lightGray
        }
        AnimatedContainer{
            Layout.fillWidth: true
            Layout.fillHeight: true
            widthToOccupe: row.width
            heightToOccupe: row.height
            isAnimated: true
        }
    }
}
