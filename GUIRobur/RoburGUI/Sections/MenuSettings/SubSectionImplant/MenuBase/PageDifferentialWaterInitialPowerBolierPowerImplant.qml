import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
import "../../../../Testing"
Rectangle {
    id : root
    property bool selection1: true
    property bool selection2: false
    property bool selection3: true
    property bool selection4: false
    property int  temperature: 0
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius : Style.dimensions.radiusCorner
    SwitcherType
    {
        visible:pipesController.isTwoPipe.value
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 8
    }
    RowLayout{
        id : selector
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection1 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection1 ? 0.7 : 0.4
            border.color:root.selection1 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:   qsTr("differenziale_acqua") +  " " + qsTr("potenza_iniziale")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.selection1 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = true
                    root.selection2 = false;
                }
            }
        }
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.selection2 ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.selection2 ? 0.7 : 0.4
            border.color:root.selection2 ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text: qsTr("Tempo_macchina") + " " + qsTr("ritardo_inserim_media_imp_separarato")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color:  root.selection2 ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.selection1 = false
                    root.selection2 = true;
                }
            }
        }
    }
    RowLayout{
        id : container
        visible: root.selection1
        anchors.top:selector.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        anchors.bottom: parent.bottom
        RoburCounterMini{
            label : qsTr("differenziale_acqua")
            candidateValue:  preferencePage.temperatureBox.value ? root.temperature.value   : (root.temperature.value * 9/5) + 32
            unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: container.width
            heightToOccupe: container.height
        }
        RoburCounterMini{
            label : qsTr("potenza_iniziale")
            btnSize : 50
            Layout.fillHeight: true
            Layout.fillWidth: true
            isAnimated: true
            widthToOccupe: container.width
            heightToOccupe: container.height
        }
    }
    Item{
        visible: root.selection2
        anchors.top:selector.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        anchors.topMargin: 8
        RowLayout{
            id : subSelector
            anchors.left: parent.left
            anchors.right: parent.right
            height: Style.dimensions.iconSize * 2
            anchors.top: parent.top
            Rectangle {
                Layout.fillHeight: true
                Layout.fillWidth: true
                radius : Style.dimensions.radiusCorner
                color: root.selection3 ? Style.colors.lightGray : Style.colors.thinGray
                opacity: root.selection3 ? 0.7 : 0.4
                border.color:root.selection3 ? Style.colors.orange : Style.colors.roburGray
                RoburText {
                    text:   qsTr("potenza_nominale_impianto")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    font.pixelSize: Style.dimensions.fontBigSize
                    color: root.selection3 ? Style.colors.orange : Style.colors.roburGray
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked:{
                        root.selection3 = true
                        root.selection4 = false;
                    }
                }
            }
            Rectangle {
                Layout.fillHeight: true
                Layout.fillWidth: true
                radius : Style.dimensions.radiusCorner
                color: root.selection4 ? Style.colors.lightGray : Style.colors.thinGray
                opacity: root.selection4 ? 0.7 : 0.4
                border.color:root.selection4 ? Style.colors.orange : Style.colors.roburGray
                RoburText {
                    text: qsTr("funzione_caldaie_ausiliarie")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    font.pixelSize: Style.dimensions.fontBigSize
                    color:  root.selection4 ? Style.colors.orange : Style.colors.roburGray
                }
                MouseArea {
                    anchors.fill: parent
                    onClicked:{
                        root.selection3 = false
                        root.selection4 = true;
                    }
                }
            }
        }
        RowLayout{
            id : firstBox
            visible: root.selection3
            anchors.top:subSelector.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.topMargin: 8
            AnimatedContainer{
                Layout.fillWidth: true
                Layout.fillHeight:  true
                widthToOccupe: firstBox.width /2
                heightToOccupe: firstBox.height / 2
                isAnimated: true
                activeColor : Style.colors.no
                inactiveColor: Style.colors.no
                ColumnLayout
                {
                    spacing: 8
                    anchors.fill: parent
                    AnimatedContainer{
                        id : idDefault
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        widthToOccupe: parent.width
                        heightToOccupe: parent.height
                        isAnimated: !idDefault.isActive
                        activeColor : Style.colors.orange
                        inactiveColor: Style.colors.lightGray
                        RoburCheckbox{
                            labelName : qsTr("default")
                            anchors.fill: parent
                            firstColor:idDefault.isActive ? Style.colors.white :  Style.colors.darkGray
                            secondColor:idDefault.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                            controlCheck: idDefault.isActive
                            onClicked:  {
                                idDefault.isActive = true
                                idCustom.isActive = false
                            }
                        }
                    }
                    AnimatedContainer{
                        id : idCustom
                        Layout.fillHeight: true
                        Layout.fillWidth: true
                        widthToOccupe: parent.width
                        heightToOccupe: parent.height
                        isAnimated:!idCustom.isActive
                        activeColor : Style.colors.orange
                        inactiveColor: Style.colors.lightGray
                        RoburCheckbox{
                            labelName: qsTr("custom")
                            anchors.fill: parent
                            firstColor:idCustom.isActive ? Style.colors.white :  Style.colors.darkGray
                            secondColor:idCustom.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                            controlCheck: idCustom.isActive
                            onClicked: {
                                idDefault.isActive = false
                                idCustom.isActive = true
                            }
                        }
                    }
                }
            }
            RoburCounterMini{
                label :  qsTr("custom")
                unit:         preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
                candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
                visible: idCustom.isActive
                Layout.fillHeight: true
                Layout.preferredWidth: parent.width/2
                isAnimated: true
                btnSize: 50
                widthToOccupe: secondBox.width
                heightToOccupe: secondBox.height
                onDownClicked: {
                }
                onUpClicked: {
                }
            }
        }
        RowLayout{
            id : secondoBox
            visible: root.selection4
            anchors.top:subSelector.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.topMargin: 8
            AnimatedContainer{
                id : idIntegrazioe
                Layout.fillHeight: true
                Layout.fillWidth: true
                widthToOccupe: secondoBox.width / 4
                heightToOccupe: secondoBox.height/ 4
                isAnimated: !check1.controlCheck
                activeColor : Style.colors.orange
                inactiveColor: Style.colors.lightGray
                RoburCheckbox{
                    id : check1
                    labelName: qsTr("integrazione")
                    anchors.fill: parent
                    firstColor:idIntegrazioe.isActive ? Style.colors.white :  Style.colors.darkGray
                    secondColor:idIntegrazioe.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                    controlCheck: idIntegrazioe.isActive
                    onClicked:  {
                        idIntegrazioe.isActive = true
                        idIntegrazioeSostituzione.isActive = false
                    }
                }
            }
            AnimatedContainer{
                id : idIntegrazioeSostituzione
                Layout.fillHeight: true
                Layout.fillWidth: true
                widthToOccupe: secondoBox.width/ 4
                heightToOccupe: secondoBox.height/ 4
                isAnimated: !check2.controlCheck
                activeColor : Style.colors.orange
                inactiveColor: Style.colors.lightGray
                RoburCheckbox{
                    id : check2
                    labelName: qsTr("integrazione_sostituzione")
                    anchors.fill: parent
                    firstColor:idIntegrazioeSostituzione.isActive ? Style.colors.white :  Style.colors.darkGray
                    secondColor:idIntegrazioeSostituzione.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                    controlCheck: idIntegrazioeSostituzione.isActive
                    onClicked:  {
                        idIntegrazioe.isActive = false
                        idIntegrazioeSostituzione.isActive = true
                    }
                }
            }
        }
    }
}
