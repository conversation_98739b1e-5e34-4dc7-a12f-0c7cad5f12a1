import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../../.."
import "../../../../Components"
Rectangle {
    id : root
    property bool activeSection1Label : true
    Layout.fillWidth: true
    Layout.fillHeight: true
    radius: Style.dimensions.radiusCorner
    RowLayout{
        id : containerSection
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 16
        height: Style.dimensions.iconSize * 2
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            radius : Style.dimensions.radiusCorner
            color: root.activeSection1Label ? Style.colors.lightGray : Style.colors.thinGray
            opacity: root.activeSection1Label ? 0.7 : 0.4
            border.color:root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            RoburText {
                text:  qsTr("config_errori_temp")
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                font.pixelSize: Style.dimensions.fontBigSize
                color: root.activeSection1Label ? Style.colors.orange : Style.colors.roburGray
            }
            MouseArea {
                anchors.fill: parent
                onClicked:{
                    root.activeSection1Label = true
                }
            }
        }
    }
    RowLayout{
        visible: root.activeSection2Label
        anchors.top:containerSection.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        AnimatedContainer{
            Layout.fillHeight: true
            Layout.fillWidth: true
            widthToOccupe: box.width
            heightToOccupe: box.height
            isAnimated: true
            activeColor : Style.colors.no
            inactiveColor: Style.colors.no
            ColumnLayout{
                id : box
                anchors.fill: parent
                AnimatedContainer{
                    id : idErroriTempDis
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    widthToOccupe: box.width
                    heightToOccupe: box.height
                    isAnimated: !check2.controlCheck
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.lightGray
                    RoburCheckbox{
                        id : check2
                        labelName: qsTr("errori_temp_disabilitati")
                        anchors.fill: parent
                        firstColor:idErroriTempDis.isActive ? Style.colors.white :  Style.colors.darkGray
                        secondColor:idErroriTempDis.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                        controlCheck: idErroriTempDis.isActive
                        onClicked:  {
                            idErroriTempDis.isActive = true
                            idseTempMandata.isActive = false
                            idseTempRitorno.isActive = false
                        }
                    }
                }
                AnimatedContainer{
                    id : idseTempMandata
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    widthToOccupe: box.width
                    heightToOccupe: box.height
                    isAnimated: !check3.controlCheck
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.lightGray
                    RoburCheckbox{
                        id : check3
                        labelName: qsTr("temperatura_mandata_media")
                        anchors.fill: parent
                        firstColor:idseTempMandata.isActive ? Style.colors.white :  Style.colors.darkerGray
                        secondColor:idseTempMandata.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                        controlCheck: idseTempMandata.isActive
                        onClicked: {
                            idErroriTempDis.isActive = false
                            idseTempMandata.isActive = true
                            idseTempRitorno.isActive = false
                        }
                    }
                }
                AnimatedContainer{
                    id : idseTempRitorno
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    widthToOccupe: box.width
                    heightToOccupe: box.height
                    isAnimated: !check4.controlCheck
                    activeColor : Style.colors.orange
                    inactiveColor: Style.colors.lightGray
                    RoburCheckbox{
                        id : check4
                        labelName: qsTr("temperatura_ritorno_media")
                        anchors.fill: parent
                        firstColor:idseTempRitorno.isActive ? Style.colors.white :  Style.colors.darkerGray
                        secondColor:idseTempRitorno.isActive ? Style.colors.darkGray :  Style.colors.darkGray
                        controlCheck: idseTempRitorno.isActive
                        onClicked:  {
                            idErroriTempDis.isActive = false
                            idseTempMandata.isActive = false
                            idseTempRitorno.isActive = true
                        }
                    }
                }
            }
        }
        RoburCounterMini{
            label : qsTr("temperatura_minima")
            candidateValue:  preferencePage.temperatureBox.value ? ambientExternSettingPage.setpoint.value   : (ambientExternSettingPage.setpoint.value * 9/5) + 32
            Layout.fillHeight: true
            Layout.preferredWidth: parent.width/2
            isAnimated: true
            btnSize: 50
            widthToOccupe: parent.width
            heightToOccupe: parent.height
        }
    }
}
