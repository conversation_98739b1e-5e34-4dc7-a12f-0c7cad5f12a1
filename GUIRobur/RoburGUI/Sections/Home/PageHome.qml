import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
Item {
    id : root
    Layout.fillHeight: true
    Layout.fillWidth: true
    ListView {
        id: listView
        //anchors.topMargin: 12
        anchors.fill: parent
        orientation: ListView.Vertical
        spacing: 6
        interactive: contentWidth > parent.width
        property bool timerToggle: false
        Timer {
            id: listViewTimer
            running: true
            repeat: true
            interval: 1000
            onTriggered: listView.timerToggle = !listView.timerToggle
        }
        model: homePage.model
        delegate: Rectangle {
            required property var modelData
            required property var index
            implicitHeight: Style.dimensions.homepageRowInt
            implicitWidth: root.width
            Layout.preferredHeight: Style.dimensions.homepageRowInt
            Layout.fillWidth: true
            color: Style.colors.white
            radius: 4
            ModelHomePageRow {
                identifier: modelData.id
                serviceIcon: modelData.serviceIcon.value
                unitStateSemaphoreColor : modelData.unitStateSemaphore.value
                serviceType: modelData.serviceType
                operatingModeIcon : modelData.operatingMode.value
                hsmPageToGo : modelData.hmsPageToGo.value
                type : modelData.filter.value
                inOutCheck : modelData.setpointInOutIndicator.value
                serviceNameIcon: modelData.serviceIconName.value
                inOutTemperatureLabel : modelData.setpointTemperature.value
                inTemperatureLabel : modelData.inTemperature.value
                outTemperatureLabel : modelData.outTemperature.value
                cogButtonAvailable : modelData.cogButtonAvailable.value
                plantOnOffLabel: modelData.plantOnOffIndicator.value
                plantOnOffColor : modelData.plantOnOffColor.value
                plantOnOffOnIsSelected: modelData.plantOnOffOnOff.value
                turnOnOffColor : modelData.turnOnOffColor.value
                turnOnOffAvaible:modelData.turnOnOffSwitchable.value
                turnOnOffStatus: modelData.turnOnOffStatus.value
                operantionToGo: modelData.operationModeDestination.value
                isSpecialIcon: modelData.isSpecialIcon
                leftIconActive: modelData.leftIconActive
                rightIconActive: modelData.rightIconActive
                leftIconUrl: modelData.leftIconUrl.value
                rightIconUrl: modelData.rightIconUrl.value
                onTurnOffOn: {
                    homePage.callTurnOnOff(index, !modelData.turnOnOffStatus.value);
                }
            }
        }
    }
}
