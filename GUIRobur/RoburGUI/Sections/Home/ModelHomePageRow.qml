import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
Item {
    id : root
    property int identifier
    property string serviceIcon
    property string unitStateSemaphoreColor
    property string operatingModeIcon
    property string serviceType
    property string operantionToGo
    property string hsmPageToGo
    property string type : ""
    property string inOutCheck
    property string serviceNameIcon
    property string inOutTemperatureLabel
    property string inTemperatureLabel
    property string outTemperatureLabel
    property string plantOnOffColor
    property string plantOnOffLabel
    property bool plantOnOffOnIsSelected
    property bool cogButtonAvailable
    property string turnOnOffColor
    property string turnOnOffStatus
    property bool turnOnOffAvaible
    property bool isSpecialIcon: false
    property string leftIconUrl
    property string rightIconUrl
    property bool leftIconActive: false
    property bool rightIconActive: false
    signal turnOffOn;
    signal iconChanged;
    anchors.fill: parent
    RowLayout {
        anchors.fill: parent
        spacing: 8
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: 1
        }
        //================================
        //=======type of service ========
        //================================
        Item {
            id: rootService
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 2
            Loader {
                id: serviceIconLoader // Diamo un nuovo id per chiarezza
                width: Style.dimensions.iconSize * 1.1
                anchors.top: parent.top
                anchors.topMargin: 8
                anchors.horizontalCenter: parent.horizontalCenter
                sourceComponent: root.isSpecialIcon ? specialIconComponent : standardIconComponent
            }
            Component {
                id: standardIconComponent
                Image {
                    width: serviceIconLoader.width // Usa le dimensioni del Loader
                    height: serviceIconLoader.width // Manteniamo l'aspetto quadrato
                    source: root.serviceIcon ? root.serviceIcon : ""
                    fillMode: Image.PreserveAspectFit
                    layer.enabled: true
                    antialiasing: true
                    layer.effect: MultiEffect {
                        brightness: 1
                        colorization: 1.0
                        colorizationColor: Style.colors.orange
                    }
                }
            }
            Component {
                id: specialIconComponent
                Item {
                    width: serviceIconLoader.width
                    height: serviceIconLoader.width
                    RowLayout {
                        anchors.fill: parent
                        spacing: 2
                        Image {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            source: root.leftIconUrl ? root.leftIconUrl : ""
                            fillMode: Image.PreserveAspectFit
                            layer.enabled: true
                            antialiasing: true
                            layer.effect: MultiEffect {
                                brightness: 1
                                colorization: 1.0
                                colorizationColor: root.leftIconActive ? Style.colors.orange : Style.colors.lightGray
                            }
                        }
                        Image {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            source: root.rightIconUrl ? root.rightIconUrl : ""
                            fillMode: Image.PreserveAspectFit
                            layer.enabled: true
                            antialiasing: true
                            layer.effect: MultiEffect {
                                brightness: 1
                                colorization: 1.0
                                colorizationColor: root.rightIconActive ? Style.colors.orange : Style.colors.lightGray
                            }
                        }
                    }
                    MouseArea {
                        anchors.fill: parent
                        hoverEnabled: true
                        cursorShape: Qt.PointingHandCursor
                        onClicked: {
                            navigator.navigate(SubSectionEnum.SUBSECTION_SETTINGS_DDCCONTROLPLANT.subSectionLabel, serviceType);
                        }
                    }
                }
            }
            Item{
                anchors.topMargin: 4
                anchors.top: serviceIconLoader.bottom
                anchors.horizontalCenter: serviceIconLoader.horizontalCenter
                anchors.bottom: parent.bottom
                width: parent.width
                RoburText{
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    text: qsTr(root.serviceNameIcon)
                    color: Style.colors.orange
                    //size: Style.dimensions.fontSmallSize
                }
            }
        }
        //=================================================
        //=======connection RED GREEN GREY ORANGE ========
        //================================================
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 1.3
            Button{
                id : btn
                anchors.centerIn: parent
                height: Style.dimensions.iconSize * 0.8
                width: Style.dimensions.iconSize * 1.3
                background: Rectangle {
                    anchors.fill: parent
                    opacity: enabled ? 1 : 0.3
                    color: root.unitStateSemaphoreColor
                    radius: 4
                }
                onClicked: {
                    navigator.navigate(root.hsmPageToGo,root.type);
                    navigator.changeSection(SectionEnum.SECTION_INFORMATION.sectionLabel);
                    hsmHeatingPage.filter.filterString = "SNOW"
                    hsmConditioningPage.filter.filterString = "SUN"
                }
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.implicitSize
            RoburIconButton {
                id: operatingMode
                icon: root.operatingModeIcon
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    operatingMode.icon
                    navigator.navigate(root.operantionToGo,root.type);
                    //navigator.pendingType = root.type
                }
            }
        }
        //=================================================
        //======= SET IN/OUT TEMPERAETURE
        //================================================
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: 160
            Image {
                id : imageContainer
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                width: height
                height: Style.dimensions.iconSize
                antialiasing: true
                mipmap: true
                source: "qrc:/icons/degre.png"
                fillMode: Image.PreserveAspectFit
                layer.enabled: true
                layer.effect: MultiEffect {
                    brightness : 1
                    colorization: 1.0
                    colorizationColor: Style.colors.darkGray
                }
            }
            Item{
                anchors.left:imageContainer.right
                anchors.right: parent.right
                anchors.top : parent.top
                anchors.bottom: parent.bottom
                RoburDataViewer{
                    id: setpointInOutIndicator
                    labelValue: root.inOutTemperatureLabel
                    labelName: qsTr("setpoint") + "\n"+ root.inOutCheck + " : "
                    labelUnit:  preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
                    anchors.topMargin: 8
                    anchors.bottomMargin:   8
                }
            }
        }
        //=================================================
        //======= ON OFF TEMPERATURE =====================
        //================================================
        // Reactive computed property using JavaScript function
        Item {
            id : rootInOutTemperature
            Layout.fillHeight: true
            Layout.preferredWidth: 120
            property string unit: (preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit"))
            function padRight(text, totalLength) {
                var result = text;
                while (result.length < totalLength)
                result += " ";
                return result;
            }
            function isNumber(value) {
                return !isNaN(parseFloat(value)) && isFinite(value);
            }
            function convertCelsiusToDisplay(celsiusValue) {
                if (!isNumber(celsiusValue))
                return NaN;
                return preferencePage.temperatureBox.value  ? (celsiusValue * 9/5 + 32) : celsiusValue;
            }
            function formatTemp(label, celsiusValue) {
                var paddedLabel = padRight(label, 5);
                var value = preferencePage.temperatureBox.value ? celsiusValue :  ( celsiusValue * 9/5) + 32
                var valueStr = isNumber(value) ? Number(value).toFixed(1) : "--.-";
                return paddedLabel + valueStr + " " + unit;
            }
            RoburRectangleBTN{
                anchors.topMargin: 8
                anchors.bottomMargin:   8
                isBTN: false
                nameBTN:    rootInOutTemperature.formatTemp("in",root.inTemperatureLabel,rootInOutTemperature.gradi)
                +"\n"
                +  rootInOutTemperature.formatTemp("out",root.outTemperatureLabel,rootInOutTemperature.gradi)
                //nameBTN: "cc"// formatTemp("in",rootInOutTemperature.valueIn,gradi) +"\n" +  formatTemp("out",rootInOutTemperature.valueOut,gradi)
                inactive: Style.colors.lightGray
                textColorInactive: Style.colors.darkGray
                distanceTextFromLeft: 4
                //writable: true
                //labelTextButton: rootInOutTemperature.paddedString1 + "\n" + rootInOutTemperature.paddedString2
                anchors.fill: parent
                //isButton: true
                onClicked: {
                    dataImplantPage.filter.filterString = type
                    navigator.navigate(SubSectionEnum.SUBSECTION_INFO_INFO_MODEL_DATA_IMPLANT.subSectionLabel);
                    navigator.changeSection(SectionEnum.SECTION_INFORMATION.sectionLabel);
                }
            }
        }
        //================================================
        //======= AGREE SECTION ==========================
        //================================================
        //======se premuto porta alla schermata 58========
        //================================================
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.iconSize * 1.5
            RoburIconButton {
                firstColor:root.plantOnOffColor
                secondColor: root.plantOnOffColor
                labelTextButton :root.plantOnOffLabel
                isSelected :root.plantOnOffOnIsSelected
                anchors.centerIn: parent
                writable: true
                onClicked: {
                    agreePage.onlyView.value = true
                    navigator.navigate(SubSectionEnum.SUBSECTION_AGREE.subSectionLabel,root.type);
                }
            }
        }
        //================================================
        //======= SETTING ================================
        //================================================
        Item {
            id : cogButton;
            //opacity: root.cogButtonAvailable.enabled ? 1 : 0 // XXX : check if it works
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.implicitSize
            RoburIconButton{
                id : iconRoundButton
                icon:  "qrc:/icons/wrench.png"
                anchors.centerIn: parent
                isButton: true
                visible:  root.cogButtonAvailable
                onClicked: {
                    navigator.navigate(SubSectionEnum.SUBSECTION_SETTINGS_DDCCONTROLPLANT.subSectionLabel, serviceType);
                }
            }
        }
        //=================================================
        //======= ON OFF CALDAIA =========================
        //================================================
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: Style.dimensions.implicitSize
            RoburIconButton{
                icon:  "qrc:/icons/on-off-button.png"
                firstColor : root.turnOnOffColor
                isSelected:root.turnOnOffStatus
                opacity : root.turnOnOffAvaible ?  1.0 : 0.3;
                enabled :root.turnOnOffAvaible
                width: height
                anchors.centerIn: parent
                onClicked:{
                    root.turnOffOn()
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 1
        }
    }
}
