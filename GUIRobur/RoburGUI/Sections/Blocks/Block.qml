import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../"
import "../../Components"
import "../../Testing"
import "../../Navigation"
import "../MenuUser"
Item {
    id : root
    anchors.fill: parent
    NameSectionBackground{
        iconPath:  "qrc:/icons/unlock.png"
        label: qsTr("button_enter_password")
    }
    RoburIconButton{
        id : seePass
        icon :  preferencePage.password.seePassword.value ?  "qrc:/icons/hide.png" : "qrc:/icons/view.png"
        isButton: true
        anchors.right: pass.left
        anchors.rightMargin: 16
        anchors.verticalCenter: pass.verticalCenter
        onClicked: {
            preferencePage.password.seePassword.value =! preferencePage.password.seePassword.value
        }
    }
    PasswordRobur{
        id : pass
        anchors.left: parent.left
        anchors.leftMargin: 128
        anchors.verticalCenter: root.verticalCenter
    }
    Numpad{
        id : numpadQML
        anchors.right: root.right
        anchors.rightMargin: 48
        anchors.verticalCenter: root.verticalCenter
        onInput:{
            preferencePage.password.setValue(val,pass.index);
            pass.index +=1;
            if(pass.index >= pass.lenght)
            pass.index=0;
        }
        onDeleteNumber: {
            preferencePage.password.deleteValue(preferencePage.password.list,pass.index)
            pass.index -=1;
            if(pass.index <= 0)
            pass.index=0;
        }
        onCheck : {
            if(preferencePage.password.checkValidate())
            {
                if(navigator.blockSectionPage.value)
                {
                    preferencePage.password.clear(preferencePage.password.list)
                    navigator.blockSectionPage.value = false
                }
                else{
                    console.debug("no state")
                }
            }
            else{
                console.debug("pass no correct")
            }
        }
    }
}
