import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../Components"
import "../../"
import "../../Testing"
Item {
    id : root
    property string idIdentification
    property string idUnitAlarm
    property string idMacchina
    property string idBlockType
    property string idAlarmType
    property string idDescription
    property bool idRestart
    property bool idOpaque
    anchors.fill: parent
    RowLayout{
        anchors.fill: parent
        spacing : 1
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text: root.idIdentification
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text: qsTr(root.idMacchina)
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                id: idServizio
                icon: root.idBlockType
                anchors.centerIn: parent
                onClicked: {
                }
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : root.idAlarmType
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburText{
                text : qsTr(root.idDescription) //FIX da inserire come sta scritto nella specifica
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton{
                id : idRestart
                icon : "qrc:/icons/restart.png"
                visible: root.idRestart
                opacity: root.idOpaque ? 0.3 : 1
                //isButton: true
                anchors.centerIn: parent
                width: height
                height: Style.preferenceSection.cnButton
                onClicked: {
                    machineManagementPage.filterProxyID.filterString = root.idUnitAlarm
                    machineManagementPage.activeFilterProxyID.value = true
                    navigator.navigate(SubSectionEnum.SUBSECTION_MACHINEMANAGEMENT.subSectionLabel);
                    navigator.changeSection(SectionEnum.SECTION_SETTINGS.sectionLabel);
                }
            }
        }
    }
}
