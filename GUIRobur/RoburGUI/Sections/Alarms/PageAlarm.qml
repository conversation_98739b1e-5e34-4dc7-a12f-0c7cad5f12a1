import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../../Components"
import "../../"
import "../../Testing"
Item {
    id : root
    Layout.fillWidth: true
    Layout.fillHeight: true
    Rectangle {
        id : rootTitle
        width: root.width
        height: Style.dimensions.heightZoneElement - 10
        anchors.top: parent.top
        radius: 4
        Layout.alignment: Qt.AlignTop
        RowLayout{
            anchors.fill: parent
            spacing: 4
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("modulo_in_allarme")
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText{
                    text: qsTr("macchina")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("tipo")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text:  qsTr("tipo") +" "+ qsTr("allarme")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("descrizione")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            Item {
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    text: qsTr("azione")
                    anchors.centerIn: parent
                    width: parent.width
                    height: parent.height
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
    Item {
        id : listModel
        anchors.top:  rootTitle.bottom
        anchors.topMargin: 8
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        ListView {
            id: listView
            anchors.topMargin: 8
            anchors.fill: parent
            orientation: ListView.Vertical
            spacing: 4
            property bool timerToggle: false
            ScrollBar.vertical: ScrollBar {
                policy: ScrollBar.AlwaysOn
                active: ScrollBar.AlwaysOn
            }
            Timer {
                id: listViewTimer
                running: true
                repeat: true
                interval: 1000
                onTriggered: listView.timerToggle = !listView.timerToggle
            }
            clip: true
            boundsBehavior: Flickable.StopAtBounds
            model: alarmPage.model
            delegate: Rectangle {
                required property var modelData
                required property var index
                implicitHeight: Style.dimensions.heightZoneElement - 10
                implicitWidth: root.width
                color: Style.colors.white
                radius: 4
                ModelAlarmConfiguration{
                    idIdentification : modelData.idIdentification.value
                    idUnitAlarm: modelData.unitAlarm.value
                    idMacchina: modelData.macchine.value
                    idBlockType: modelData.blockType.value
                    idAlarmType: modelData.alarm.value
                    idDescription: modelData.description.value
                    idRestart: modelData.restart.value
                    idOpaque : modelData.isOpaque.value
                }
            }
        }
    }
}
