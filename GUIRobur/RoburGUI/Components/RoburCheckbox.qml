import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick
import QtQuick.Layouts
import QtQuick.Effects
import "../Components"
import ".."
import "../Testing"

Item {
    id: root
    property string labelName: ""
    property color firstColor: Style.colors.darkerGray
    property color secondColor: Style.colors.gray
    property bool isTexted: true
    property alias controlCheck: control.checked
    property int borderTratt: 3
    property int dashCount: 3
    property int dashHeight: 6
    property int dashWidth: 2
    property real dashSpacing: (Style.dimensions.checkSize - (dashCount * dashHeight)) / (dashCount - 1)
    // === FONT ===
    FontLoader {
        id: fontLoader
        source: "qrc:/fonts/Rubik-SemiBold.ttf"
    }
    width: 100
    height: Style.dimensions.checkSize * 2
    signal clicked
    RowLayout {
        anchors.fill: parent
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: root.isTexted ? control.width : Style.dimensions.checkSize
            Layout.fillWidth: root.isTexted ? false : true
            CheckBox {
                id: control
                checked: root.controlCheck
                anchors.centerIn: parent
                anchors.verticalCenter: parent.verticalCenter
                // === INDICATORE CUSTOM ===
                indicator: Rectangle {
                    x: control.leftPadding
                    y: parent.height / 2 - height / 2
                    implicitWidth: Style.dimensions.checkSize
                    implicitHeight: Style.dimensions.checkSize
                    color: root.secondColor
                    radius: 4
                    opacity: root.enabled ? 1 : 0.3
                    // Bordi verticali sinistro e destro
                    Repeater {
                        model: dashCount
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.left: parent.left
                        Rectangle {
                            width: root.dashWidth
                            height: root.dashHeight
                            color: "black"
                            opacity: root.enabled ? 0 : 0.3
                            radius: 4
                            x: 0
                            y: index * (root.dashHeight + root.dashSpacing)
                        }
                    }
                    Repeater {
                        model: dashCount
                        Rectangle {
                            width: root.dashWidth
                            height: root.dashHeight
                            color: "black"
                            radius: 4
                            opacity: root.enabled ? 0 : 0.3
                            x: 0
                            y: index * (root.dashHeight + root.dashSpacing)
                            anchors.right: parent.right
                        }
                    }
                    Repeater {
                        model: dashCount
                        Rectangle {
                            width: root.dashHeight
                            height: root.dashWidth
                            color: "black"
                            x: index * (root.dashHeight + root.dashSpacing)
                            radius: 4
                            opacity: root.enabled ? 0 : 0.3
                            y: 0
                            anchors.bottom: parent.bottom
                        }
                    }
                    Repeater {
                        model: dashCount
                        Rectangle {
                            width: root.dashHeight
                            height: root.dashWidth
                            color: "black"
                            radius: 4
                            opacity: root.enabled ? 0 : 0.3
                            x: index * (root.dashHeight + root.dashSpacing)
                            y: 0
                            anchors.top: parent.top
                        }
                    }
                    // Centro del CheckBox
                    Rectangle {
                        anchors.centerIn: parent
                        width: 14
                        height: 14
                        radius: 4
                        color: control.checked ? root.firstColor : root.secondColor
                        opacity: root.enabled ? 1 : 0.5
                        visible: control.checked
                    }
                }
            }
        }
        // === TESTO ===
        Item {
            Layout.fillHeight: root.isTexted
            Layout.fillWidth: isTexted
            visible: isTexted
            RoburText {
                id: text
                text: root.labelName
                font.family: fontLoader.font.family
                font.capitalization: Font.AllUppercase
                color: control.checked ? root.firstColor : root.secondColor
                font.pointSize: Style.dimensions.fontMediumSize
                fontSizeMode: Text.Fit
                width: parent.width
                height: parent.height
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
    MouseArea {
        anchors.fill: parent
        onClicked: {
            root.clicked();
        }
    }
}
