import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import "../"
import "../Components"
import "../Testing"
/*
*   Size                :   grandezza del testo, da passare a font.pointSize, perchè con
altrimenti fontSizeMode non funzione
color               :   colore del testo, da appliccare
minimumPointSize    :   corrisponde alla minima grandezza che il testo può assumere quando è
attivo la modolità Text.FIT
*
*/
Text {
    id : root
    FontLoader { id: fontLoader; source: "qrc:/fonts/Rubik-SemiBold.ttf" }
    property int size:  Style.dimensions.fontMediumSize
    property color textColor : Style.colors.darkGray
    property bool bold: root.bold
    color : textColor
    minimumPointSize: 4
    font.family: fontLoader.font.family
    font.pointSize: root.size
    font.capitalization: Font.AllUppercase
    font.bold: false
    fontSizeMode: Text.Fit
    wrapMode: Text.WordWrap
    verticalAlignment: Text.AlignVCenter
}
