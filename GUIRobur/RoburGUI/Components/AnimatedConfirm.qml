import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ".."
Rectangle {
    id: root
    property alias smallAnimation : small
    property string label
    property bool firstClick: false
    property bool secondClick: false
    property bool isZActive: false
    border.color: Style.colors.gray
    border.width: 4
    property color firstClickColor: Style.colors.darkGray
    property color secondClickColor: Style.colors.orange
    property color inactiveColor: Style.colors.white
    property bool isAnimated: false
    property int widthSize: 100
    property int heightSize: 100
    property int  sizeLabelFont: Style.dimensions.fontBigSize
    property int xParent: 0
    property int yParent: 0
    property int animationDuration : 500
    implicitHeight: root.heightSize
    implicitWidth: root.widthSize
    property  int widthToOccupe: 0
    property  int heightToOccupe: 0
    signal goTo
    signal clicked
    radius: 100
    color:root.inactiveColor
    RoburText{
        id : labelText
        textColor: Style.colors.darkGray
        anchors.centerIn: parent
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        text : root.label
    }
    property bool comeback: false
    function singleClick(){
        print("Single click")
        small.start()
        firstClick = false
        goTo()
        clicked()
    }
    Timer{
        id:timer
        interval: 500
        onTriggered: singleClick()
    }
    MouseArea {
        anchors.fill: parent
        onClicked:
        {
            if(root.isAnimated && !timer.running)
            {
                timer.start()
                bigg.start()
                comeback = true
                firstClick = true
            }
        }
    }
    ParallelAnimation{
        id : small
        NumberAnimation{
            target: root
            property: "implicitWidth"
            from:   root.widthToOccupe
            to : root.widthSize
        }
        NumberAnimation{
            target: root
            property: "implicitHeight"
            from:  root.heightToOccupe
            to : root.heightSize
        }
        NumberAnimation{
            target: root
            property: "sizeLabelFont"
            from: Style.dimensions.fontBiggerSize
            to :Style.dimensions.fontBigSize
        }
        ColorAnimation {
            target: root
            property: "color"
            from: root.firstClickColor
            to:  root.inactiveColor
        }
        ColorAnimation {
            target: labelText
            property: "textColor"
            from:   root.secondClickColor
            to:  Style.colors.darkGray
        }
        onFinished: {
            isZActive =false
        }
    }
    ParallelAnimation{
        id : bigg
        NumberAnimation{
            target: root
            property: "implicitWidth"
            from:   root.widthSize
            to : root.widthToOccupe
            duration: root.animationDuration
        }
        NumberAnimation{
            target: root
            property: "implicitHeight"
            from:  root.heightSize
            to : root.heightToOccupe
            duration: root.animationDuration
        }
        NumberAnimation{
            target: root
            property: "sizeLabelFont"
            from: Style.dimensions.fontBigSize
            to : Style.dimensions.fontBiggerSize
            duration: root.animationDuration
        }
        ColorAnimation {
            target: root
            property: "color"
            from: root.firstClickColor
            to: root.secondClickColor
        }
        ColorAnimation {
            target: labelText
            property: "textColor"
            from: root.secondClickColor
            to:  root.firstClickColor
        }
        onStarted: {
            isZActive =true
        }
    }
}
