import ".."
import QtQuick 2.15
import QtQuick.Effects
Item {
    id: root
    property alias icon: iconElement.source
    property alias borderLanguage: roburIcon.border.width
    property color firstColor:  Style.colors.orange
    property color secondColor:  Style.colors.gray
    property color color: root.firstColor
    property bool filled: false
    property bool isrotated: false
    property real imgRateo: 0.90
    property color imageColor: filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor
    property bool isSelected: false
    //multieffect and status buttons:
    property bool isMultiEffectActive: true
    // set the button is clickable
    property bool isEnabled : true
    property bool isPressed: false
    property bool isButton: false
    //signals
    signal clicked()
    signal pressed()
    signal released()
    signal pressAndHold()
    signal tick()
    signal buttonClicked()
    implicitHeight: 100
    implicitWidth: 100
    onFilledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    onEnabledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor: root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    Rectangle {
        id: roburIcon
        anchors.fill: parent
        radius: roburIcon.height / 2
        color: root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent"
        border.width: 7
        border.color: root.isSelected ?  root.firstColor : root.secondColor
        Item{
            anchors.centerIn: parent
            width: height
            height: roburIcon.height * imgRateo
            Image {
                id: iconElement
                source: icon
                sourceSize.height: roburIcon.height * imgRateo
                sourceSize.width: roburIcon.width * imgRateo
                fillMode: Image.PreserveAspectFit
                anchors.centerIn: parent
                mipmap: true
                antialiasing: true
                layer.enabled: true
            }
        }
    }
    MouseArea {
        id: buttonMouseArea
        anchors.fill: parent
        onClicked: {
            if(isButton)
            {
                root.isPressed = true
                pressedTimer.start();
            }
            else
            {
                root.clicked();
            }
            root.buttonClicked();
        }
        onPressed: {
            if (filled)
            {
                root.opacity = 0.7;
            }
            else
            {
                roburIcon.color = "#20" + root.color.toString().replace("#", ""); //Style.colors.thinGray
            }
            root.pressed();
        }
    }
}
