import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import "../Components"
import ".."
import "../Testing"
Rectangle {
    id: root
    property real candidateValue: 0
    property string unit: ""
    property bool isActive: false
    property bool isZActive: false
    property bool needToFix: true
    property color activeColor: Style.colors.darkGray
    property color inactiveColor: Style.colors.lightGray
    property string iconPath
    property string label: ""
    property bool enableTick: false
    property bool isAnimated: false
    property int widthSize: 220
    property int heightSize: 110
    property int textSize: 35
    property int btnSize: 35
    property bool changeValue: true
    implicitHeight: root.heightSize
    implicitWidth: root.widthSize
    property int  sizeLabelFont: Style.dimensions.fontBigSize
    property  int widthToOccupe: widthSize
    property  int heightToOccupe: heightSize
    signal upClicked
    signal downClicked
    signal clicked
    signal disableAll
    radius: 4
    color: root.isActive ? root.activeColor : root.inactiveColor
    Item{
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        anchors.rightMargin: 4
        anchors.leftMargin: 4
        height: root.textSize * 1.5
        RoburText {
            id : idMeasure
            text: (root.needToFix ? candidateValue.toFixed(1) : candidateValue) + root.unit
            font.pointSize: root.textSize
            color: root.isActive ? Style.colors.white : root.activeColor
            width: parent.width
            height: parent.height / 2
            anchors.centerIn: parent
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    Image {
        visible: root.iconPath
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.margins: 16
        id: probeIcon
        source: iconPath
        width: 40
        height: 40
        layer.enabled: true
        antialiasing: true
        fillMode: Image.PreserveAspectFit
        layer.effect: MultiEffect {
            brightness : 1
            colorization: 1.0
            colorizationColor:root.isActive ? Style.colors.white : activeColor
        }
    }
    MouseArea {
        anchors.fill: parent
        onClicked:
        {
            root.isActive =  !root.isActive
            root.clicked()
            root.disableAll()
        }
    }
    Item{
        id : bottomText
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 4
        anchors.left: parent.left
        anchors.leftMargin: 4
        anchors.right: bottonsContainer.left
        //anchors.rightMargin: 4
        height: root.sizeLabelFont * 1.50
        RoburText {
            id: counterLabel
            text: root.label
            font.pointSize:  root.sizeLabelFont
            color: root.isActive ? Style.colors.white : root.activeColor
            width: parent.width
            height: parent.height
        }
    }
    ColumnLayout {
        id : bottonsContainer
        visible: root.isActive && root.changeValue
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.rightMargin: 8
        anchors.bottom: parent.bottom
        width: 50
        RoburIconButton {
            id: upButton
            firstColor: root.isActive ? Style.colors.white : Style.colors.no
            Layout.preferredWidth: root.btnSize
            Layout.preferredHeight: root.btnSize
            Layout.alignment: Qt.AlignCenter
            icon: "qrc:/icons/arrow.png"
            isSelected: root.isActive
            onClicked: {
                root.upClicked()
            }
            onTick: root.upClicked()
            rotation: 90
        }
        RoburIconButton {
            id: downButton
            firstColor: root.isActive ? Style.colors.white : Style.colors.no
            Layout.preferredWidth: root.btnSize
            Layout.preferredHeight: root.btnSize
            Layout.alignment: Qt.AlignCenter
            icon: "qrc:/icons/arrow.png"
            isSelected: root.isActive
            rotation: 270
            onClicked: {
                root.downClicked()
            }
            onTick: root.downClicked()
        }
    }
    states: [
    State {
        name: "small"
        when: !root.isActive
    },
    State {
        name: "big"
        when: root.isActive
    }
    ]
    transitions: [
    Transition {
        from: "small"
        to : "big"
        ParallelAnimation{
            id : bigg
            NumberAnimation{
                target: root
                property: "implicitWidth"
                from: widthSize
                to : widthToOccupe
            }
            NumberAnimation{
                target: root
                property: "implicitHeight"
                from: heightSize
                to : heightToOccupe
            }
            NumberAnimation{
                target: root
                property: "sizeLabelFont"
                from: Style.dimensions.fontBigSize
                to : Style.dimensions.fontBiggerSize
            }
            onStarted: {
                isZActive =true
            }
        }
    },
    Transition {
        from: "big"
        to : "small"
        ParallelAnimation{
            id : small
            NumberAnimation{
                target: root
                property: "implicitWidth"
                from: widthToOccupe
                to : widthSize
            }
            NumberAnimation{
                target: root
                property: "implicitHeight"
                from: heightToOccupe
                to : heightSize
            }
            NumberAnimation{
                target: root
                property: "sizeLabelFont"
                from: Style.dimensions.fontBiggerSize
                to :Style.dimensions.fontBigSize
            }
            onFinished: {
                isZActive =false
            }
        }
    }
    ]
}
