import QtQuick 2.15
import "../"
import "../Testing"
Item {
    property alias listview: listview
    property alias index: listview.currentIndex
    property alias lenght: listview.count
    property alias currenteItem: listview.currentItem
    property alias model: listview.model
    width: 10 * Style.dimensions.iconSize;
    height: Style.dimensions.iconSize;
    ListView {
        id : listview
        width: 10 * Style.dimensions.iconSize;
        height: Style.dimensions.iconSize;
        orientation :ListView.Horizontal
        interactive: false
        spacing: 8
        model: preferencePage.password.model
        focus: true
        delegate: PasswordNumber
        {
            id : passNumber
            required property var modelData
            required property var index
            numbers :  preferencePage.password.seePassword.value? modelData.number.value : "*"
            color: ListView.isCurrentItem ? Style.colors.orange : Style.colors.lightGray
            onCurrentitem : {
                listview.currentIndex = index
            }
        }
    }
}
