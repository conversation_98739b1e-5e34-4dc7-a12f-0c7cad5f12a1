import QtQuick 2.15
import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Effects
import QtQuick.Controls.Basic
import "../"
import "../Components"
import "../Testing"
Item{
    id: root
    property string iconPath : ""
    property alias label: sectionName.text
    property alias color: sectionName.textColor
    property alias colorImage: colorway.colorizationColor
    property bool isASwitcher: false
    property real gradientLight: 0
    property string firstOption: ""
    property string secondOption: ""
    property bool isSelected : false
    property bool filled: false
    property real imgRateo: 0.5
    property int distanceX : icon.x
    property int distanceY : icon.y
    property alias xName: sectionName.x
    property alias yName: sectionName.y
    anchors.fill: parent
    Rectangle {
        id : contanierCard
        anchors.fill: parent
        radius: 4
        color: isSelected ? Style.colors.darkOrange : Style.colors.white
        RowLayout{
            anchors.left: parent.left
            anchors.right: parent.right
            height :icon.height * 2
            Item{
                Layout.fillHeight: true
                Layout.preferredWidth: Style.dimensions.iconSize * 2
                Image {
                    id: icon
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.margins: 16
                    source: root.iconPath
                    width: height
                    height: Style.dimensions.iconSize
                    fillMode: Image.PreserveAspectFit
                    mipmap: true
                    antialiasing: true
                    smooth: true
                    layer.enabled: true
                    visible: false
                }
                MultiEffect {
                    id : colorway
                    source: icon
                    anchors.fill: icon
                    brightness : 1
                    colorization: 1.0
                    colorizationColor: root.isSelected ? Style.colors.white : Style.colors.darkGray
                }
            }
            Item{
                Layout.fillHeight: true
                Layout.fillWidth: true
                RoburText {
                    id : sectionName
                    anchors.fill: parent
                    textColor:  root.isSelected ? Style.colors.white : Style.colors.darkGray
                }
            }
        }
    }
}
