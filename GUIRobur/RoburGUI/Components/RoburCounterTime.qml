import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import "../"
import "../Components"
import "../Testing"
Rectangle {
    id: root
    property string candidateHour
    property string candidateMinute
    property string unit: ""
    property bool isActive: false
    property bool isZActive: false
    property color activeColor: Style.colors.darkGray
    property color inactiveColor: Style.colors.lightGray
    property string iconPath
    property string label: ""
    property bool enableTick: false
    property bool isAnimated: false
    signal upClicked
    signal downClicked
    signal clicked
    signal disableAll
    property int widthSize: 220
    property int heightSize: 110
    property int  sizeLabelFont: Style.dimensions.fontBigSize
    property int xParent: 0
    property int yParent: 0
    implicitHeight: heightSize
    implicitWidth: widthSize
    property  int widthToOccupe: widthSize
    property  int heightToOccupe: heightSize
    property int textSize: 35
    property int btnSize: 35
    radius: 4
    color: root.isActive ? root.activeColor : root.inactiveColor
    Item{
        anchors.left: root.left
        //anchors.left: label.left
        anchors.verticalCenter: root.verticalCenter
        //anchors.bottom: label.top
        //anchors.right: buttonContainer.left
        anchors.right: parent.right
        height: root.textSize * 1.5
        RoburText {
            id : hour
            text: root.candidateHour + " : " + root.candidateMinute
            font.pointSize: root.textSize
            color: root.isActive ? Style.colors.white : root.activeColor
            //font.weight: Font.Medium
            width: parent.width
            height: parent.height
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            // anchors.verticalCenter:root.verticalCenter
        }
    }
    Image {
        visible: root.iconPath
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.margins: 16
        id: probeIcon
        source: iconPath
        width: 40
        height: 40
        layer.enabled: true
        antialiasing: true
        fillMode: Image.PreserveAspectFit
        layer.effect: MultiEffect {
            brightness : 1
            colorization: 1.0
            colorizationColor:root.isActive ? Style.colors.white : activeColor
        }
    }
    MouseArea {
        anchors.fill: parent
        onClicked:
        {
            root.isActive =  !root.isActive
            root.disableAll()
            // if(root.isActive && root.isAnimated)
            // {
                //     bigg.start()
                // }
                // else
                // {
                    //     small.start()
                    // }
                }
            }
            RoburText {
                id : label
                anchors.bottom: parent.bottom
                anchors.left: parent.left
                anchors.leftMargin: 32
                anchors.bottomMargin: 10
                text: root.label
                font.pixelSize: root.sizeLabelFont
                color: root.isActive ? Style.colors.white : root.activeColor
            }
            ColumnLayout {
                id : buttonContainer
                visible: root.isActive
                anchors.top: parent.top
                anchors.right: parent.right
                anchors.rightMargin: 8
                anchors.bottom: parent.bottom
                width: 50
                RoburIconButton {
                    id: upButton
                    icon: "qrc:/icons/down-arrow.png"
                    firstColor: root.isActive ? Style.colors.white : Style.colors.no
                    Layout.preferredWidth: root.btnSize
                    Layout.preferredHeight: root.btnSize
                    Layout.alignment: Qt.AlignCenter
                    isSelected: root.isActive
                    onClicked: {
                        root.upClicked()
                    }
                    onTick: root.upClicked()
                    transform: Rotation {
                        origin.x: upButton.width / 2
                        origin.y: upButton.height / 2
                        angle: 180
                    }
                }
                RoburIconButton {
                    icon: "qrc:/icons/down-arrow.png"
                    id: downButton
                    firstColor: root.isActive ? Style.colors.white : Style.colors.no
                    Layout.preferredWidth: root.btnSize
                    Layout.preferredHeight: root.btnSize
                    Layout.alignment: Qt.AlignCenter
                    isSelected: root.isActive
                    onClicked: {
                        root.downClicked()
                    }
                    onTick: root.downClicked()
                }
            }
            states: [
            State {
                name: "small"
                when: !root.isActive
            },
            State {
                name: "big"
                when: root.isActive
            }
            ]
            transitions: [
            Transition {
                from: "small"
                to : "big"
                ParallelAnimation{
                    id : bigg
                    NumberAnimation{
                        target: root
                        property: "implicitWidth"
                        from: widthSize
                        to : widthToOccupe
                    }
                    NumberAnimation{
                        target: root
                        property: "implicitHeight"
                        from: heightSize
                        to : heightToOccupe
                    }
                    NumberAnimation{
                        target: root
                        property: "sizeLabelFont"
                        from: Style.dimensions.fontBigSize
                        to : Style.dimensions.fontBiggerSize
                    }
                    onStarted: {
                        isZActive =true
                    }
                }
            },
            Transition {
                from: "big"
                to : "small"
                ParallelAnimation{
                    id : small
                    NumberAnimation{
                        target: root
                        property: "implicitWidth"
                        from: widthToOccupe
                        to : widthSize
                    }
                    NumberAnimation{
                        target: root
                        property: "implicitHeight"
                        from: heightToOccupe
                        to : heightSize
                    }
                    NumberAnimation{
                        target: root
                        property: "sizeLabelFont"
                        from: Style.dimensions.fontBiggerSize
                        to :Style.dimensions.fontBigSize
                    }
                    onFinished: {
                        isZActive =false
                    }
                }
            }
            ]
        }
