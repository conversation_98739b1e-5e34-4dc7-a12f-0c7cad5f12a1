import ".."
import QtQuick 2.15
import QtQuick.Effects
Item {
    id: root
    property alias content: contentArea.children
    property alias border: roburIcon.border.width
    property alias labelTextButton: text.text
    property bool isSetPreferredWidth: false
    property color firstColor:  Style.colors.orange
    property color secondColor:  Style.colors.gray
    property string labelName: ""
    property string labelValue: ""
    property string labelUnit: ""
    property color color: root.firstColor
    property bool filled: false
    property bool isrotated: false
    property real imgRateo: 0.5
    property color imageColor: filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor
    property bool isSelected: false
    //multieffect and status buttons:
    property bool isMultiEffectActive: true
    property bool isEnabled : true
    property bool isPressed: false
    property bool isTick: false
    property bool isButton: false
    //signals
    signal clicked()
    signal pressed()
    signal released()
    signal pressAndHold()
    signal tick()
    signal buttonClicked()
    implicitHeight: Style.dimensions.defaultHeightIconButton
    implicitWidth: root.writable ? (root.isSetPreferredWidth ? (text.contentWidth + Style.dimensions.offsetText) :  Style.dimensions.defaultHeightIconButton) :  Style.dimensions.defaultHeightIconButton
    onFilledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    onEnabledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor: root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    Rectangle {
        id: roburIcon
        anchors.fill: parent
        radius: roburIcon.height / 2
        color: root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent"
        border.width: 3
        border.color: isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
        (root.isSelected ?  root.firstColor : root.secondColor)
        rotation: isrotated ? 180 : 0
        Item{
            anchors.centerIn: parent
            width: height
            height: roburIcon.height * imgRateo
            RoburText {
                id: text
                text: root.labelName + " "
                color:  isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                (root.isSelected ?  root.firstColor : root.secondColor)
            }
            RoburText {
                anchors.left: text.right
                text:  root.labelValue + " " + root.labelUnit
                color:  isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                (root.isSelected ?  root.firstColor : root.secondColor)
            }
            // MultiEffect {
                //     id : colorway
                //     visible: isMultiEffectActive
                //     source: iconElement
                //     anchors.fill: iconElement
                //     brightness : 1.0
                //     colorization: 1.0
                //     colorizationColor: isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                //     (root.isSelected ?  root.firstColor : root.secondColor)
                // }
            }
        }
        Item {
            id: contentArea
            anchors.centerIn: parent
        }
        MouseArea {
            id: buttonMouseArea
            anchors.fill: parent
            onClicked: {
                if(isEnabled)
                {
                    if(isButton)
                    {
                        root.isPressed = true
                        pressedTimer.start();
                    }
                    else
                    {
                        root.clicked();
                    }
                    root.buttonClicked();
                }
            }
            onPressed: {
                if(isEnabled)
                {
                    root.isPressed = true
                    if (filled)
                    root.opacity = 0.8;
                    else
                    roburIcon.color = "#20" + root.color.toString().replace("#", ""); //Style.colors.thinGray
                    root.pressed();
                }
            }
            onPressAndHold: {
                if(isEnabled)
                {
                    pressedTimer.start();
                    tickerTimer.start()
                }
            }
            onReleased: {
                if(isEnabled)
                {
                    if (filled)
                    {
                        root.opacity = 1;
                    }
                    else
                    {
                        roburIcon.color = "transparent";
                    }
                    root.released();
                    pressedTimer.stop();
                    tickerTimer.stop();
                }
            }
        }
        Timer {
            id: pressedTimer
            interval: 100
            running: false
            repeat: false
            onTriggered: {
                root.isPressed = false
                root.clicked();
            }
        }
        Timer {
            id: tickerTimer
            interval: 100
            running: false
            repeat: true
            onTriggered: {
                tick();
            }
        }
    }
