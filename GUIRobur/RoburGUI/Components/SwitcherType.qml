import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ".."
import "../Components"
import "../Testing"
Item {
    id : root
    width: 140
    height: 70
    signal switchToCool
    signal switchToHeat
    RoburIconButton
    {
        id: cool
        icon: "qrc:/icons/snowflake.png"
        anchors.left: parent.left
        anchors.leftMargin:8
        isSelected: true
        anchors.verticalCenter: parent.verticalCenter
        onClicked: {
            heat.isSelected = false;
            cool.isSelected = true
            root.switchToCool()
        }
    }
    Rectangle
    {
        width: 4
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        color: Style.colors.darkerGray
        anchors.horizontalCenter:  parent.horizontalCenter
        radius: 4
    }
    RoburIconButton
    {
        id : heat
        icon: "qrc:/icons/sun.png"
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
        anchors.rightMargin: 8
        onClicked: {
            heat.isSelected = true;
            cool.isSelected = false
            root.switchToHeat()
        }
    }
}
