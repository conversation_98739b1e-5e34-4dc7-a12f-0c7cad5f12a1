import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ".."
Rectangle {
    id: root
    property bool isActive
    property bool isZActive: false
    property color activeColor: Style.colors.darkGray
    property color inactiveColor: Style.colors.white
    property bool isAnimated : true
    property int widthSize: 100
    property int heightSize: 100
    property int  sizeLabelFont: Style.dimensions.fontBigSize
    property int xParent: 0
    property int yParent: 0
    implicitHeight: root.heightSize
    implicitWidth: root.widthSize
    property  int widthToOccupe: 0
    property  int heightToOccupe: 0
    signal clicked
    signal disableAll
    radius: 4
    color: root.isActive ? root.activeColor : root.inactiveColor
    MouseArea {
        anchors.fill: parent
        onClicked:
        {
            if(isAnimated)
            {
                root.isActive =  !root.isActive
                root.disableAll()
                root.clicked()
            }
        }
    }
    states: [
    State {
        name: "small"
        when: !root.isActive
    },
    State {
        name: "big"
        when: root.isActive
    }
    ]
    transitions: [
    Transition {
        from: "small"
        to : "big"
        ParallelAnimation{
            id : bigg
            NumberAnimation{
                target: root
                property: "implicitWidth"
                from: widthSize
                to : widthToOccupe
            }
            NumberAnimation{
                target: root
                property: "implicitHeight"
                from: heightSize
                to : heightToOccupe
            }
            NumberAnimation{
                target: root
                property: "sizeLabelFont"
                from: Style.dimensions.fontBigSize
                to : Style.dimensions.fontBiggerSize
            }
            onStarted: {
                isZActive =true
            }
        }
    },
    Transition {
        from: "big"
        to : "small"
        ParallelAnimation{
            id : small
            NumberAnimation{
                target: root
                property: "implicitWidth"
                from: widthToOccupe
                to : widthSize
            }
            NumberAnimation{
                target: root
                property: "implicitHeight"
                from: heightToOccupe
                to : heightSize
            }
            NumberAnimation{
                target: root
                property: "sizeLabelFont"
                from: Style.dimensions.fontBiggerSize
                to :Style.dimensions.fontBigSize
            }
            onFinished: {
                isZActive =false
            }
        }
    }
    ]
}
