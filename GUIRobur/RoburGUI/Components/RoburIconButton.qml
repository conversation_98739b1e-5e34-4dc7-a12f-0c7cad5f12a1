import ".."
import QtQuick 2.15
import QtQuick.Effects
Item {
    id: root
    property alias icon: iconElement.source
    property alias content: contentArea.children
    property alias border: roburIcon.border.width
    property alias labelTextButton: text.text
    property bool isSetPreferredWidth: false
    property bool writable: false
    property color firstColor:  Style.colors.orange
    property color secondColor:  Style.colors.gray
    property color color: root.firstColor
    property bool filled: false
    property bool isrotated: false
    property real imgRateo: 0.5
    property color imageColor: filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor
    property bool isSelected: false
    //multieffect and status buttons:
    property bool isMultiEffectActive: true
    // set the button is clickable
    property bool isEnabled : true
    property bool isPressed: false
    property bool isTick: false
    property bool isButton: false
    property  alias mouse: buttonMouseArea
    //signals
    signal clicked()
    signal pressed()
    signal released()
    signal pressAndHold()
    signal tick()
    signal buttonClicked()
    implicitHeight: Style.dimensions.defaultHeightIconButton
    implicitWidth: root.writable ? (root.isSetPreferredWidth ? (text.contentWidth + Style.dimensions.offsetText) :  Style.dimensions.defaultHeightIconButton) :  Style.dimensions.defaultHeightIconButton
    onFilledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    onEnabledChanged: {
        roburIcon.color = root.filled ? root.enabled ? root.firstColor: root.secondColor : "transparent";
        imageColor = filled ? Style.colors.white : root.enabled ? root.firstColor : root.secondColor;
    }
    Rectangle {
        id: roburIcon
        anchors.fill: parent
        radius: roburIcon.height / 2
        color: root.filled ? root.enabled ? root.firstColor : root.secondColor : "transparent"
        border.width: 3
        border.color: isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
        (root.isSelected ?  root.firstColor : root.secondColor)
        rotation: isrotated ? 180 : 0
        Item{
            anchors.centerIn: parent
            width: height
            height: roburIcon.height * imgRateo
            Image {
                id: iconElement
                source: icon
                sourceSize.height: roburIcon.height * imgRateo
                sourceSize.width: roburIcon.width * imgRateo
                fillMode: Image.PreserveAspectFit
                anchors.centerIn: parent
                mipmap: true
                antialiasing: true
                layer.enabled: true
                visible: false
            }
            RoburText {
                id: text
                text: labelTextButton
                visible: writable
                color:  isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                (root.isSelected ?  root.firstColor : root.secondColor)
                anchors.centerIn: parent
                bold: true
            }
            // Text {
                //     id: text
                //     text: labelTextButton
                //     visible: writable
                //     color:  isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                //     (root.isSelected ?  root.firstColor : root.secondColor)
                //     anchors.centerIn: parent
                //     font.capitalization: Font.AllUppercase
                //     font.bold: true
                //     elide: Text.ElideMiddle
                //     fontSizeMode: Text.Fit
                // }
                MultiEffect {
                    id : colorway
                    visible: isMultiEffectActive
                    source: iconElement
                    anchors.fill: iconElement
                    brightness : 1.0
                    colorization: 1.0
                    colorizationColor: isButton ?(root.isPressed ? root.firstColor :  root.secondColor) :
                    (root.isSelected ?  root.firstColor : root.secondColor)
                }
            }
        }
        /*  MultiEffect {
            source: roburIcon
            anchors.fill: roburIcon
            shadowBlur: 0.1
            shadowEnabled: isButton ?(root.isPressed ?true : false) :  (root.isSelected ? true : false)
            shadowColor: "black"
            paddingRect: Qt.rect(roburIcon.x,roburIcon.y,11,11)
            autoPaddingEnabled : true
        }
        */
        Item {
            id: contentArea
            anchors.centerIn: parent
        }
        MouseArea {
            id: buttonMouseArea
            anchors.fill: parent
            onClicked: {
                if(isEnabled)
                {
                    if(isButton)
                    {
                        root.isPressed = true
                        pressedTimer.start();
                    }
                    else
                    {
                        root.clicked();
                    }
                    root.buttonClicked();
                }
            }
            onPressed: {
                if(isEnabled)
                {
                    root.isPressed = true
                    if (filled)
                    root.opacity = 0.8;
                    else
                    roburIcon.color = "#20" + root.color.toString().replace("#", ""); //Style.colors.thinGray
                    root.pressed();
                }
            }
            onPressAndHold: {
                if(isEnabled)
                {
                    pressedTimer.start();
                    tickerTimer.start()
                }
            }
            onReleased: {
                if(isEnabled)
                {
                    if (filled)
                    {
                        root.opacity = 1;
                    }
                    else
                    {
                        roburIcon.color = "transparent";
                    }
                    root.released();
                    pressedTimer.stop();
                    tickerTimer.stop();
                }
            }
        }
        Timer {
            id: pressedTimer
            interval: 100
            running: false
            repeat: false
            onTriggered: {
                root.isPressed = false
                root.clicked();
            }
        }
        Timer {
            id: tickerTimer
            interval: 100
            running: false
            repeat: true
            onTriggered: {
                tick();
            }
        }
    }
