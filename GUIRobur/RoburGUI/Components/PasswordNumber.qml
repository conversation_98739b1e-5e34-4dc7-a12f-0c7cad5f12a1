import QtQuick
import "../Components"
import "../"
Rectangle{
    id : root
    property alias numbers : numbers.text
    signal currentitem
    signal turn
    height: Style.dimensions.iconSize + 10
    width: height
    radius : Style.dimensions.radiusCorner
    color: Style.colors.no
    RoburText {
        id : numbers
        anchors.centerIn: parent
        size: Style.dimensions.fontMediumSize - 1
    }
    MouseArea{
        anchors.fill: parent;
        onClicked: {
            root.currentitem();
            root.turn();
        }
    }
}
