import QtQuick 2.15
import ".."
Rectangle {
    id : root
    property string message
    radius : Style.dimensions.radiusCorner
    color: Style.colors.orange
    Item{
        anchors.fill: parent
        RoburText {
            text: qsTr(message)
            width: parent.width
            height: parent.height
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            color: Style.colors.white
        }
    }
}
