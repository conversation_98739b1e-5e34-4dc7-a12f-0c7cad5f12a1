import QtQuick 2.15
import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Effects
import QtQuick.Controls.Basic
import "../"
import "../Components"
import "../Testing"
Rectangle {
    id: root
    property string labelName: ""
    property string labelValue: ""
    property string labelUnit: ""
    property bool readOnly: true
    property bool isLabelNameTakeSameSpaceLabelValue: true //spazione che prende il testo rispetto al valore inserito
    property int labelNameSpace
    property bool isSetTexValueSpace: true
    property int setTextValueSpace
    property int sizeText: Style.dimensions.fontMediumSize
    property bool isFoces
    property int borderData : 10
    property color onFocus  :  Style.colors.darkGray
    property color noOnFocus: Style.colors.white
    anchors.fill: parent
    FontLoader {
        id: fontLoader
        source: "qrc:/fonts/Rubik-SemiBold.ttf"
    }
    color: root.isFoces ? root.onFocus : root.noOnFocus
    radius: 4
    RowLayout {
        anchors.fill: parent
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: root.isLabelNameTakeSameSpaceLabelValue ? true : false
            Layout.preferredWidth:  root.isLabelNameTakeSameSpaceLabelValue ? 0 : root.labelNameSpace
            color: root.isFoces ?root.onFocus: Style.colors.no
            RoburText {
                text: root.labelName + " "
                color: root.isFoces ?Style.colors.white: Style.colors.darkGray
                anchors.left: parent.left
                anchors.leftMargin: 4
                height: parent.height
                width: parent.width
                size: root.sizeText
                verticalAlignment: Text.AlignVCenter
            }
        }
        Rectangle {
            Layout.preferredHeight: parent.height - root.borderData
            Layout.fillWidth: root.isSetTexValueSpace ? true : false
            Layout.preferredWidth:  root.isSetTexValueSpace ? 0 : root.setTextValueSpace
            color: root.isFoces ?Style.colors.white: Style.colors.lightGray
            radius: 4
            RoburText {
                id: data
                text: root.labelValue + " " + root.labelUnit
                color: Style.colors.darkGray
                size: root.sizeText
                width: parent.width
                height: parent.height
                anchors.centerIn: parent
                verticalAlignment: Text.AlignVCenter
                horizontalAlignment: Text.AlignHCenter
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: 1
        }
    }
}
