import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ".."
Rectangle{
    id : root
    property string nameBTN
    property string iconBTN
    property bool isPressed: false
    property bool isBTN: true
    property color activeColor: Style.colors.orange
    property color inactive:  Style.colors.white
    property color textColorActive:  Style.colors.white
    property color textColorInactive:   Style.colors.darkGray
    property int distanceTextFromLeft: 32
    radius : Style.dimensions.radiusCorner
    anchors.fill: parent
    color: root.isPressed ? root.activeColor : root.inactive
    signal clicked;
    FontLoader { id: fontLoader; source: "qrc:/fonts/Rubik-SemiBold.ttf" }
    RoburText{
        font.family: !root.isBTN ?  "monospace" : fontLoader.font.family
        font.bold: !root.isBTN ? true : false
        id : resetError
        color:  root.isPressed ? root.textColorActive : root.textColorInactive
        text: root.nameBTN
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.verticalCenter: parent.verticalCenter
        anchors.leftMargin: root.distanceTextFromLeft
        anchors.rightMargin: root.distanceTextFromLeft
    }
    RoburIconButton{
        visible: root.isBTN
        id : resetBTN
        icon: root.iconBTN
        isButton: true
        anchors.right: parent.right
        anchors.rightMargin:  8
        anchors.verticalCenter: parent.verticalCenter
        //isPressed: root.isPressed
        isPressed: root.isPressed
        firstColor: Style.colors.white
    }
    MouseArea{
        anchors.fill: parent
        onClicked: {
            isPressed = true
            pressedTimer.start();
        }
        onPressed: {
            root.isPressed = true
        }
        onPressAndHold: {
            pressedTimer.start();
        }
    }
    Timer {
        id: pressedTimer
        interval: 250
        running: false
        repeat: false
        onTriggered: {
            root.isPressed = false
            root.clicked();
        }
    }
}
