import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import ".."
Rectangle {
    id : root
    width: 500
    height: 250
    radius : Style.dimensions.radiusCorner
    color: Style.colors.lightGray
    border.color: Style.colors.orange
    border.width: 4
    signal close
    Item{
        width: parent.width
        height: 100
        anchors.bottom: container.top
        RoburText {
            text: qsTr("confermi")
            width: parent.width
            height: parent.height
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }
    Item{
        id : container
        anchors.verticalCenter: parent.verticalCenter
        anchors.left: parent.left
        anchors.right: parent.right
        height:  100
        RowLayout{
            anchors.fill: parent
            anchors.leftMargin: 32
            anchors.rightMargin: 32
            AnimatedConfirm
            {
                label :  qsTr("yes")
                Layout.fillWidth: true
                Layout.fillHeight: true
                widthToOccupe: container.width
                heightToOccupe: container.height
                isAnimated: true
                onClicked:  {
                    if(ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue === "cool")
                    {
                        ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue = "heat"
                    }
                    else
                    {
                        ddcConfigure.twoPipesSwitch.activeChilHeatGS.currentValue = "cool"
                    }
                    ddcConfigure.saveTwoPipesSwitch()
                    root.close()
                }
            }
            AnimatedConfirm
            {
                label :  qsTr("no")
                Layout.fillWidth: true
                Layout.fillHeight: true
                widthToOccupe: container.width
                heightToOccupe: container.height
                isAnimated: true
                onClicked:  {
                    root.close()
                }
            }
        }
    }
}
