import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
Item {
    id: root
    width:  50
    height: 20
    signal clicked()
    signal changed()
    property alias isChecked : control.checked
    property alias switchWidth: controlIndicator.width
    property string firstOptionText: ""
    property string secondOptionText: ""
    property string activeBaseColor: Style.colors.orange
    property string inactiveBaseColor: Style.colors.white
    property string disabledBaseColor: Style.colors.lightGray
    property string pivotUpColor: Style.colors.lightGray
    property string pivotDownColor: Style.colors.white
    property string pivotDisabledColor: Style.colors.thinGray
    property color pivotUpBorderColor: Style.colors.orange
    property color pivotDownBorderColor: Style.colors.lightGray
    property color pivotDisabledBorderColor: Style.colors.darkGray
    property color colorTextFistOption: Style.colors.white
    property color colorTextSecondOption:Style.colors.darkGray
    property color yesChecked: Style.colors.white
    property color noChecked: Style.colors.darkGray
    FontLoader { id: fontLoader; source: "qrc:/fonts/Rubik-SemiBold.ttf" }
    Switch {
        id: control
        anchors.fill: parent
        enabled: true
        font.pixelSize: 16
        width: parent.width
        height: parent.height
        indicator: Rectangle {
            id: controlIndicator
            width:  control.width
            height: control.height - 4
            x: control.leftInset
            y: control.height / 2 - height / 2
            radius: (control.height - 4) / 2
            color: control.checked ? activeBaseColor : inactiveBaseColor
            border.color: control.checked ? activeBaseColor : disabledBaseColor
            Rectangle {
                id : controlBackGround
                x: control.checked ? parent.width - width : 0
                width: control.height - 4
                height: width
                radius: height / 2
                color: control.checked ? pivotUpColor : pivotDownColor
                border.color: control.checked ? (control.down ? pivotDownBorderColor : pivotUpBorderColor) : pivotDisabledBorderColor
            }
        }
        contentItem: Text {
            text:control.checked ? root.firstOptionText : root.secondOptionText
            font.family: fontLoader.font.family
            font.capitalization: Font.AllUppercase
            font.pointSize: Style.dimensions.fontDefaultSize
            opacity: control.enabled ? 1.0 : 0.3
            color: control.checked ? root.colorTextFistOption : root.colorTextSecondOption
            verticalAlignment: Text.AlignVCenter
            leftPadding: control.indicator.width + control.spacing
        }
        onClicked: {
            root.clicked();
        }
    }
}
