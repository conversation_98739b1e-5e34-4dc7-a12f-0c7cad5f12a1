import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
Loader {
    id: loaderLazy
    property bool when: false
    property alias sourceComponentLazy: loaderLazy.sourceComponent
    visible: when && active && status == Loader.Ready
    // Start inactive.
    active: false
    // When the condition becomes true, activate the loader permanently.
    onWhenChanged: {
        if (when) {
            active = true;
        }
    }
    // Also check the condition on startup.
    Component.onCompleted: {
        if (when) {
            active = true;
        }
    }
}
