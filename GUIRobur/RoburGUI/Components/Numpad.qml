import QtQuick 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls 2.15
import ".."
import "../Testing"
Item {
    id: root
    property alias firstButton: firstButton
    signal input(int val)
    signal deleteNumber
    signal check
    implicitHeight: 300
    implicitWidth:  250
    GridLayout {
        anchors.margins:  16
        anchors.fill: parent
        anchors.centerIn: parent
        columns: 3
        rows: 4
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                id : firstButton
                writable: true
                labelTextButton: "1"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(1);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "2"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(2);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "3"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(3);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "4"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(4);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "5"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(5);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "6"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(6);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "7"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(7);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "8"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(8);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "9"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(9);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                id : deleteBTN
                icon : "qrc:/icons/arrowsss.png"
                rotation: 180
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.deleteNumber();
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                writable: true
                labelTextButton: "0"
                isButton: true
                anchors.centerIn: parent
                onClicked: {
                    root.input(0);
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.fillWidth: true
            RoburIconButton {
                icon : "qrc:/icons/arrow.png"
                isButton: true
                rotation: 180
                anchors.centerIn: parent
                onClicked: {
                    root.check();
                }
            }
        }
    }
}
