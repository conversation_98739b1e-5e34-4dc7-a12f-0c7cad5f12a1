pragma Singleton
import QtQuick 2.15
QtObject {
    id: style
    property QtObject colors: QtObject {
        id: colors
        property color white: "white"
        property color black: "#000000"
        property color red: "#BB1C1C"
        property color green: "#B59E3D"
        property color blue: "#007FFF"
        property color darkBlue: "#131246"
        property color orange: "#FF7119"
        property color darkOrange: "#E05C0B"
        property color darkerOrange: "#377213"
        property color roburGray: "#414141"
        property color thinGray: "#F5F5F5"
        property color lightGray: "#D9D9D9"
        property color gray: "#B4B1B0"
        property color darkGray: "#868686"
        property color darkerGray: "#636363"
        property color no: "transparent"
        property color background: "#E9E9EA"
        property color ierdna: "pink"
        // virtual Keyboard
        property color verydark: "#1E1B18"
        property color verydarkgreyish: "#35322F"
        property color shadowcolor: "#3F000000"
        property color colorInfoBar: "#ffc60e"
        property color colorToggleOn: "#3cb371"
        property color colorToggleOFF: "#ff6347"
        property color fire: "crimson";
        property color snow: "steelblue";
        property color contrast: "#00B37E"
        property color cyan: "#1CCECB"
        property color deepBlue: "#9E3DB5"
        property color orangeContrast: "#FF7F00"
        property color t: Qt.rgba(Math.random(), Math.random(), Math.random(), 1)
    }
    property QtObject dimensions: QtObject {
        id: dimensions
        property int  homepageRowInt: 70
        property int radiusCorner : 4
        property int fontTinySize: 7
        property int fontSmallSize: 8
        property int fontDefaultSize: 10
        property int fontMediumSize: 12
        property int fontBigSize: 14
        property int fontLilBiggerSize: 17
        property int fontBiggerSize: 20
        property int fontLargeSize: 25
        property int fontXLargeSize: 30
        property int fontXXLargeSize: 35
        property int windowWidth: 800
        property int windowHeight: 480
        property int sidebarWidth: 80
        property int topbarHeight: 80
        property int widthRect: 85
        property int heightZoneElement: 65
        property int iconSize: 35
        property int implicitSize : 48
        property int cnIcon: 25
        property int cnButton: 45
        property int checkSize: 28
        property int cnButtonRoburCounter: 45
        property int heightConfiguration: 50
        property int buttonNavigator: 60
        property int defaultHeightIconButton: 48
        property int defaulWidthtIconButton: 48
        property int offsetText: 15
        property int heightBanner: 200
        property int widthBanner: 400
        property int  heightElements: 75
    }
    property QtObject preferenceSection: QtObject {
        id: prefDimSection
        property int changePage: 40
        property int cnButton: 40
        property int language: 50
    }
}
