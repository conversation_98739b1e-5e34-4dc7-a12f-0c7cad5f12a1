import QtQuick 2.15
import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Effects
import QtQuick.Controls.Basic
import "../"
import "../Components"
import "../Testing"
Rectangle {
    id : root
    property alias icon : icon.source
    property alias nameSubSection: nameSubSection.text
    property alias iconFirstBTN: firstSectionBTN.icon
    property alias iconSecondBTN: secondSectionBTN.icon
    property bool isSelected : false
    property bool isTwoTypeService : true
    property string subsectionToGo
    property string subsectionToGoFirst
    property string subsectionToGoSecond
    property bool isFirstPressed: false
    property bool isSecondPressed: false
    property string type : "SNOW"
    property string timerToGo
    FontLoader { id: fontLoader; source: "qrc:/fonts/Rubik-Bold.ttf" }
    signal change
    signal firstPress
    signal secondPress
    signal clickedSection
    anchors.fill: parent
    radius: 4
    color: isSelected ? Style.colors.orange : Style.colors.white
    Image {
        id: icon
        anchors.left:  root.left
        anchors.top:  root.top
        anchors.margins: 8
        height: Style.dimensions.buttonNavigator
        width: height
        fillMode: Image.PreserveAspectFit
        layer.enabled: true
        mipmap: true
        antialiasing: true
        smooth: true
        visible: false
    }
    MultiEffect {
        id : colorway
        source: icon
        anchors.fill: icon
        brightness : 1
        colorization: 1.0
        colorizationColor: root.isSelected ? Style.colors.white : Style.colors.darkGray
    }
    Item{
        height: Style.dimensions.buttonNavigator
        anchors.verticalCenter: icon.verticalCenter
        anchors.left: !isTwoTypeService? firstSectionBTN.right : icon.right
        anchors.right: !isTwoTypeService? secondSectionBTN.left : parent.right
        anchors.rightMargin: 8
        anchors.leftMargin: 8
        RoburText{
            id : nameSubSection
            color: isSelected ? Style.colors.white : Style.colors.darkGray
            size: Style.dimensions.fontBigSize
            anchors.fill: parent
            horizontalAlignment :  Text.AlignHCenter
            anchors.horizontalCenter: parent.horizontalCenter
        }
    }
    RoburIconButton {
        id: firstSectionBTN
        icon :"qrc:/icons/snowflake.png"
        visible: !isTwoTypeService
        height: width
        width: Style.dimensions.buttonNavigator
        anchors.bottom:  root.bottom
        anchors.left:  root.left
        anchors.margins: 8
        firstColor:  Style.colors.white
        secondColor: Style.colors.gray
        onClicked: {
            root.timerToGo = subsectionToGoFirst;
            root.isSelected = true
            root.isFirstPressed = !root.isFirstPressed;
            firstSectionBTN.isSelected = true;
            timerTwoSection.start();
            root.type = "SNOW"
            root.clickedSection()
        }
    }
    RoburIconButton {
        id: secondSectionBTN
        icon :"qrc:/icons/sun.png"
        visible: !isTwoTypeService
        anchors.bottom:  root.bottom
        anchors.right:  root.right
        anchors.margins: 8
        height: width
        width: Style.dimensions.buttonNavigator
        firstColor:  Style.colors.white
        secondColor: Style.colors.gray
        onClicked: {
            root.timerToGo = subsectionToGoSecond;
            root.isSelected = true;
            root.isSecondPressed = !root.isSecondPressed;
            secondSectionBTN.isSelected = true
            timerTwoSection.start();
            root.type =  "SUN"
        }
    }
    MouseArea{
        visible: isTwoTypeService
        anchors.fill: parent
        onClicked: {
            root.isSelected = !isSelected;
            root.timerToGo = subsectionToGo;
            timerOneSection.start();
        }
    }
    Timer {
        id : timerTwoSection
        interval: 200; running: false; repeat: false;
        onTriggered:
        {
            if(root.isFirstPressed)
            {
                root.firstPress()
            }
            if(root.isSecondPressed)
            {
                root.secondPress()
            }
            root.clickedSection()
            root.isSelected = false;
            root.isSecondPressed = false
            root.isFirstPressed = false;
            secondSectionBTN.isSelected = false
            firstSectionBTN.isSelected = false
            navigator.navigate(root.timerToGo,root.type);
        }
    }
    Timer {
        id : timerOneSection
        interval: 200; running: false; repeat: false;
        onTriggered:
        {
            root.change()
            root.isSelected = false
            console.debug(root.type);
            navigator.navigate(root.subsectionToGo,root.type);
            root.clickedSection()
        }
    }
}
