import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../"
import "../Components"
import "../Testing"

Rectangle {
    id: sidebar
    Layout.preferredWidth: Style.dimensions.sidebarWidth
    Layout.fillHeight: true
    Layout.bottomMargin: -3
    Layout.leftMargin: -3
    Layout.topMargin: -3
    border.width: 3
    border.color: Style.colors.orange
    color: Style.colors.roburGray
    signal deleteActiveFilter
    ColumnLayout {
        id: optionsButtons
        anchors.top: parent.top
        anchors.topMargin: 16
        anchors.left: parent.left
        anchors.right: parent.right
        spacing: 16
        RoburIconButton {
            id: rootButtonHome
            icon: "qrc:/icons/home.png"
            secondColor: Style.colors.white
            isSelected: navigator.currentSection === SectionEnum.SECTION_HOME.sectionLabel
            enabled: !hourglass.commandLoading
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            Rectangle {
                id: sidebarDelegatePivot1
                height: 3
                visible: navigator.currentSection === SectionEnum.SECTION_HOME.sectionLabel
                width: (optionsButtons.width - parent.width) / 2
                color: "transparent"
                border.width: 3
                border.color: Style.colors.orange
                anchors.right: parent.right
                anchors.rightMargin: -sidebarDelegatePivot1.width
                anchors.verticalCenter: parent.verticalCenter
            }
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigate(SubSectionEnum.SUBSECTION_DASHBOARD.subSectionLabel);
                navigator.changeSection(SectionEnum.SECTION_HOME.sectionLabel); // HOME
            }
        }
        RoburIconButton {
            id: rootButtonInfo
            isSelected: navigator.currentSection === SectionEnum.SECTION_INFORMATION.sectionLabel
            icon: "qrc:/icons/information.png"
            enabled: !hourglass.commandLoading
            secondColor: Style.colors.white
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            Rectangle {
                id: sidebarDelegatePivot2
                height: 3
                visible: navigator.currentSection === SectionEnum.SECTION_INFORMATION.sectionLabel
                width: (optionsButtons.width - parent.width) / 2
                color: "transparent"
                border.width: 2
                border.color: Style.colors.orange
                anchors.right: parent.right
                anchors.rightMargin: -sidebarDelegatePivot2.width
                anchors.verticalCenter: parent.verticalCenter
            }
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigate(SubSectionEnum.SUBSECTION_INFODASHBOARD.subSectionLabel);// info
                navigator.changeSection(SectionEnum.SECTION_INFORMATION.sectionLabel);
            }
        }
        RoburIconButton {
            id: rootButtonSettings
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            enabled: !hourglass.commandLoading
            icon: "qrc:/icons/folderL.png"
            isSelected: navigator.currentSection === SectionEnum.SECTION_SETTINGS.sectionLabel
            secondColor: Style.colors.white
            Rectangle {
                id: sidebarDelegatePivot4
                height: 3
                visible: navigator.currentSection === SectionEnum.SECTION_SETTINGS.sectionLabel
                width: (optionsButtons.width - parent.width) / 2
                color: "transparent"
                border.width: 2
                border.color: Style.colors.orange
                anchors.right: parent.right
                anchors.rightMargin: -sidebarDelegatePivot4.width
                anchors.verticalCenter: parent.verticalCenter
            }
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigate(SubSectionEnum.SUBSECTION_SETTINGS.subSectionLabel);
                navigator.changeSection(SectionEnum.SECTION_SETTINGS.sectionLabel); // menuuser
            }
        }
        RoburIconButton {
            id: rootButtonUser
            icon: "qrc:/icons/cogs.png"
            isSelected: navigator.currentSection === SectionEnum.SECTION_USERMENU.sectionLabel
            secondColor: Style.colors.white
            enabled: !hourglass.commandLoading
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            Rectangle {
                id: sidebarDelegatePivot3
                height: 3
                visible: navigator.currentSection === SectionEnum.SECTION_USERMENU.sectionLabel
                width: (optionsButtons.width - parent.width) / 2
                color: "transparent"
                border.width: 2
                border.color: Style.colors.orange
                anchors.right: parent.right
                anchors.rightMargin: -sidebarDelegatePivot3.width
                anchors.verticalCenter: parent.verticalCenter
            }
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigate(SubSectionEnum.SUBSECTION_USERMENU.subSectionLabel);
                navigator.changeSection(SectionEnum.SECTION_USERMENU.sectionLabel); // menuuser
            }
        }
        RoburIconButton {
            id: rootButtonAlarms
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            enabled: !hourglass.commandLoading
            icon: "qrc:/icons/alert.png"
            isSelected: navigator.currentSection === SectionEnum.SECTION_ALARMS.sectionLabel
            secondColor: Style.colors.white
            Rectangle {
                id: sidebarDelegatePivot5
                height: 3
                visible: navigator.currentSection === SectionEnum.SECTION_ALARMS.sectionLabel
                width: (optionsButtons.width - parent.width) / 2
                color: "transparent"
                border.width: 2
                border.color: Style.colors.orange
                anchors.right: parent.right
                anchors.rightMargin: -sidebarDelegatePivot5.width
                anchors.verticalCenter: parent.verticalCenter
            }
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigate(SubSectionEnum.SUBSECTION_ALARMS.subSectionLabel);
                navigator.changeSection(SectionEnum.SECTION_ALARMS.sectionLabel); // alarms
            }
        }
        RoburIconButton {
            isButton: true
            opacity: preferencePage.blockKeyBoard.value ? 1 : 0
            enabled: !hourglass.commandLoading
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            Layout.alignment: Qt.AlignCenter
            firstColor: Style.colors.darkOrange
            secondColor: Style.colors.gray
            icon: "qrc:/icons/unlock.png"
            onClicked: {
                if (preferencePage.blockKeyBoard.value) {
                    sidebar.deleteActiveFilter();
                    navigator.blockSectionPage.value = true;
                }
            }
        }
        RoburIconButton {
            Layout.preferredWidth: 50
            Layout.preferredHeight: 50
            enabled: !hourglass.commandLoading
            visible: navigator.canInavigateBackSetter.value && navigator.currentSection !== SectionEnum.SECTION_HOME.sectionLabel
            isButton: true
            Layout.alignment: Qt.AlignCenter
            firstColor: Style.colors.darkOrange
            secondColor: Style.colors.orange
            icon: "qrc:/icons/return.png"
            onClicked: {
                sidebar.deleteActiveFilter();
                navigator.navigateBack();
            }
        }
    }
    onDeleteActiveFilter: {
        machineManagementPage.activeFilterProxyID.value = false;
    }
}
