import QtQuick
import QtQuick.Controls 2.15
import QtQuick.Layouts
import QtQuick.Effects
import "../"
import "../Components"
import "../Testing"
Item {
    id : root
    Layout.fillHeight: true
    Layout.fillWidth: true
    Rectangle{
        id : rect
        color: Qt.rgba(1,0.55, 0.05, 0.9)
        radius: 4
        anchors.fill: parent
        Item{
            height: parent.height
            width: parent.width
            anchors.centerIn: parent
            BusyIndicator {
                id: control
                anchors.centerIn: parent
                contentItem: Item {
                    implicitWidth: 64
                    implicitHeight: 64
                    Item {
                        id: item
                        x: parent.width / 2 - 32
                        y: parent.height / 2 - 32
                        width: 64
                        height: 64
                        opacity: control.running ? 1 : 0
                        Behavior on opacity {
                            OpacityAnimator {
                                duration: 250
                            }
                        }
                        RotationAnimator {
                            target: item
                            running: control.visible && control.running
                            from: 0
                            to: 360
                            loops: Animation.Infinite
                            duration: 1250
                        }
                        Repeater {
                            id: repeater
                            model: 7
                            Rectangle {
                                x: item.width / 2 - width / 2
                                y: item.height / 2 - height / 2
                                implicitWidth: 10
                                implicitHeight: 10
                                radius: 5
                                color: "#FFFFFF"
                                transform: [
                                Translate {
                                    y: -Math.min(item.width, item.height) * 0.5 + 5
                                },
                                Rotation {
                                    angle: index / repeater.count * 360
                                    origin.x: 5
                                    origin.y: 5
                                }
                                ]
                            }
                        }
                    }
                }
            }
        }
        MouseArea
        {
            anchors.fill: parent
            onClicked:
            {
                console.debug("saving")
            }
        }
    }
}
