import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Effects
import "../"
import "../Components"
import "../Testing"
Item
{
    id : topBarInformation
    Layout.fillWidth: true
    Layout.preferredHeight: Style.dimensions.heightZoneElement
    Layout.alignment: Qt.AlignTop
    RowLayout{
        anchors.fill: parent
        spacing: 4
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true
            RowLayout{
                anchors.fill: parent
                Item {
                    Layout.fillHeight: true
                    Layout.preferredWidth: sectionTitle.width
                    RoburText{
                        id: sectionTitle
                        text: qsTr(navigator.currentTitleSection.value)
                        //size: Style.dimensions.fontBigSize
                        size : navigator.currentTitleSubSection.value.length == 0 ?Style.dimensions.fontBigSize : Style.dimensions.fontDefaultSize
                        anchors.verticalCenter: parent.verticalCenter
                        anchors.left: parent.left
                        anchors.leftMargin: 2
                        // color : navigator.currentTitleSubSection.value.length == 0 ? Style.colors.orange : Style.colors.darkGray
                        states :[
                        State {
                            name: "small"
                            when: navigator.currentTitleSubSection.value.length == 0
                            PropertyChanges {
                                target: sectionTitle
                                color : Style.colors.orange
                            }
                        },
                        State {
                            name: "big"
                            when: navigator.currentTitleSubSection.value.length != 0
                            PropertyChanges {
                                target: sectionTitle
                                color : Style.colors.darkGray
                            }
                        }
                        ]
                        transitions: [
                        Transition {
                            from: "small"
                            to: "big"
                            NumberAnimation{
                                target: sectionTitle
                                property: "size"
                                from: Style.dimensions.fontBigSize
                                to : Style.dimensions.fontDefaultSize
                                easing.type: Easing.InOutQuad
                            }
                            ColorAnimation {
                                from: Style.colors.orange
                                to: Style.colors.darkGray
                                easing.type: Easing.InOutQuad
                                duration:  250
                            }
                        },
                        Transition {
                            from: "big"
                            to: "small"
                            NumberAnimation{
                                target: sectionTitle
                                property: "size"
                                from: Style.dimensions.fontDefaultSize
                                to : Style.dimensions.fontBigSize
                                easing.type: Easing.InOutQuad
                            }
                            ColorAnimation {
                                from: Style.colors.darkGray
                                to: Style.colors.orange
                                easing.type: Easing.InOutQuad
                                duration: 250
                            }
                        }
                        ]
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.preferredWidth:Style.dimensions.cnIcon
                    RoburIconButton {
                        id: reference
                        icon: "qrc:/icons/arrow.png"
                        isrotated: true
                        height: width
                        width: Style.dimensions.cnIcon
                        isSelected: true
                        anchors.centerIn: parent
                        visible: navigator.currentTitleSubSection.value.length == 0 ? false : true
                    }
                }
                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    RoburText {
                        id: currentSubSection
                        text: navigator.currentTitleSubSection.value.length == 0 ? "" : qsTr(navigator.currentTitleSubSection.value)
                        color: Style.colors.orange
                        //size: Style.dimensions.fontBigSize
                        anchors.fill: parent
                    }
                }
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 50
            RoburIconButton {
                icon: navigator.pendingType === "SUN" ?  "qrc:/icons/sun.png" :  "qrc:/icons/snowflake.png"
                isrotated: true
                height: width
                width: Style.dimensions.cnIcon * 1.3
                isSelected: true
                anchors.centerIn: parent
                //visible: navigator.pendingType.length == 0 ? false : true
                visible: navigator.canInavigateBackSetter.value && navigator.pendingType.length != 0//.length == 0 ? false : true
            }
        }
        Item{
            Layout.fillHeight: true
            Layout.preferredWidth: 150
            Image {
                id : sourceImage
                source: "qrc:/icons/heat-control.png"
                width: height
                height: Style.dimensions.iconSize * 1.3
                mipmap: true
                antialiasing: true
                layer.enabled: true
                fillMode: Image.PreserveAspectFit
                visible: false
                anchors.verticalCenter: parent.verticalCenter
                anchors.left: parent.left
                anchors.leftMargin: 16
            }
            MultiEffect{
                source: sourceImage
                anchors.fill: sourceImage
                brightness : 1.0
                colorization: 1.0
                colorizationColor:Style.colors.darkerGray
            }
            RoburDataViewer
            {
                anchors.right:  parent.right
                anchors.verticalCenter: parent.verticalCenter
                anchors.left: sourceImage.right
                labelValue:  preferencePage.temperatureBox.value ? preferencePage.temperature.value  : (preferencePage.temperature.value * 9/5) + 32
                labelUnit : preferencePage.temperatureBox.value ? qsTr("simbolo_gradi_centigradi") : qsTr("simbolo_gradi_fahrenheit")
                sizeText: Style.dimensions.fontBiggerSize
                color: Style.colors.no
            }
        }
        Item {
            Layout.fillHeight: true
            Layout.preferredWidth: 140
            RoburText {
                id: infoTime
                text :dataTime.time.value
                size : Style.dimensions.fontLilBiggerSize
                anchors.right: parent.right
                anchors.rightMargin: 8
                anchors.top: parent.top
                anchors.topMargin: 4
                color: Style.colors.darkGray
            }
            RoburText {
                id: infoDate
                text: dataTime.date.value
                size: Style.dimensions.fontBigSize
                anchors.right: parent.right
                anchors.rightMargin: 8
                anchors.topMargin: 4
                anchors.top: infoTime.bottom
                color: Style.colors.darkGray
            }
        }
        // Item {
            //     Layout.fillHeight: true
            //     Layout.preferredWidth: 120
            //     RoburSwitch
            //     {
                //         anchors.fill: parent
                //         isChecked:pipesController.isTwoPipe.value
                //         onClicked:
                //         {
                    //             pipesController.isTwoPipe.value = ! pipesController.isTwoPipe.value
                    //             console.debug("pipesController.isTwoPipe.value : " + pipesController.isTwoPipe.value)
                    //         }
                    //     }
                    // }
                }
            }
