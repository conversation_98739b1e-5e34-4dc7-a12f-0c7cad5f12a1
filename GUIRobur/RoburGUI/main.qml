import QtQuick
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "Testing"
import "Components"
import "Sections/Blocks"
import "Sections"

ApplicationWindow {
    id: mainArea
    visible: true
    color: Style.colors.lightGray
    width: Style.dimensions.windowWidth
    height: Style.dimensions.windowHeight
    minimumWidth: 800
    minimumHeight: 480
    Timer {
        id: timer
        interval: 200 // 1000 ms = 1 second
        running: true
        repeat: true
        onTriggered: {
            dataTime.updateDataTime();
        }
    }
    ConditionalLoader {
        anchors.fill: parent
        when: true
        sourceComponentLazy: GeneralLayout {}
    }
    LoaderPage {
        id: loader
        heightToFill: parent.height
        width: parent.width
        whenLoad: navigator.blockSectionPage.value
        whenDisappier: preferencePage.password.checkValidate()
        Block {
            anchors.fill: parent
            // No need to set visible; shown/hidden by y animation
        }
    }
}
